<?php $__env->startSection('panel'); ?>
    <div class="row responsive-row">
        <?php $__currentLoopData = $configurations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k => $configuration): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-xxl-3 col-lg-4 col-sm-6 config-col">
                <div class="system-configure">
                    <div class="system-configure__header d-flex justify-content-between align-items-center">
                        <div class="system-configure__title d-flex align-items-center gap-2">
                            <div class="icon"><i class=" <?php echo e(@$configuration->icon); ?>"></i></div>
                            <h6 class="mb-0 config-name"><?php echo e(__(ucwords(@$configuration->title))); ?></h6>
                        </div>
                        <div class="form-check form-switch form--switch pl-0 form-switch-success">
                            <input class="form-check-input configuration-switch" type="checkbox" role="switch"
                                id="<?php echo e($k); ?>" data-key="<?php echo e($k); ?>" <?php if(gs($k)): echo 'checked'; endif; ?>
                                data-configuration='<?php echo json_encode($configuration, 15, 512) ?>'>
                        </div>
                    </div>
                    <div class="system-configure__content">
                        <p class="desc">
                            <?php if(gs($k)): ?>
                                <?php echo e(__(@$configuration->description_disabled)); ?>

                            <?php else: ?>
                                <?php echo e(__(@$configuration->description_enabled)); ?>

                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('breadcrumb-plugins'); ?>
    <div class="input-group">
        <span class="input-group-text bg--white border-0">
            <i class="las la-search"></i>
        </span>
        <input class="form-control bg--white highLightSearchInput border-0 ps-0" type="search"
            placeholder="<?php echo app('translator')->get('Search configuration'); ?>..." data-parent="config-col" data-search="config-name">
    </div>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        "use strict";
        (function($) {
            $(".configuration-switch").on('change', function(e) {

                const url = "<?php echo e(route('admin.setting.system.configuration.update', ':key')); ?>";
                const key = $(this).data('key');
                const configuration = $(this).data('configuration');
                const $this = $(this);
                const isChecked = $this.is(':checked');
                $.ajax({
                    type: "get",
                    url: url.replace(":key", key),
                    data: "data",
                    success: function(resp) {
                        if (resp.success) {
                            if (resp.new_status) {
                                notify('success', `${configuration.title} enabled successfully`);
                                $this.closest(".system-configure").find('.desc').text(configuration
                                    .description_disabled);
                            } else {
                                notify('success', `${configuration.title} disabled successfully`);
                                $this.closest(".system-configure").find('.desc').text(configuration
                                    .description_enabled);
                            }
                        } else {
                            notify('error', resp.message);
                            $this.attr('checked', !isChecked)
                        }
                    },
                    error: function(resp) {
                        $this.attr('checked', !isChecked)
                    }
                });
            });

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\swt_app\core\resources\views/admin/setting/configuration.blade.php ENDPATH**/ ?>