9999999999O:25:"App\Models\GeneralSetting":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:16:"general_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:54:{s:2:"id";i:1;s:9:"site_name";s:3:"SWT";s:8:"cur_text";s:3:"SAR";s:7:"cur_sym";s:3:"SAR";s:10:"email_from";s:20:"<EMAIL>";s:15:"email_from_name";s:13:"{{site_name}}";s:14:"email_template";s:4270:"<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Notification</title>
    <style>
        /* General Styles */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Open Sans', Arial, sans-serif;
            background-color: #f4f4f4;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
            width: 100%;
        }

        img {
            display: block;
            border: 0;
            line-height: 0;
        }

        a {
            color: #ff600036;
            text-decoration: none;
        }

        .email-wrapper {
            width: 100%;
            background-color: #f4f4f4;
            padding: 30px 0;
        }

        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .email-header {
            background-color: #ff600036;
            color: #000;
            text-align: center;
            padding: 20px;
            font-size: 16px;
            font-weight: 600;
        }

        /* Logo */
        .email-logo {
            text-align: center;
            padding: 40px 0;
        }

        .email-logo img {
            max-width: 180px;
            margin: 0 auto;
        }

        /* Content */
        .email-content {
            padding: 0 30px 30px 30px;
            text-align: left;
        }

        .email-content h1 {
            font-size: 22px;
            color: #414a51;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .email-content p {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.6;
            margin: 20px 0;
        }

        .email-divider {
            margin: 20px auto;
            width: 60px;
            border-bottom: 3px solid #ff600036;
        }

        /* Footer */
        .email-footer {
            background-color: #ff600036;
            color: #000;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            padding: 20px;
        }


        /* Responsive Design */
        @media only screen and (max-width: 480px) {
            .email-content {
                padding: 20px;
            }

            .email-header,
            .email-footer {
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="email-wrapper">
        <table class="email-container" cellpadding="0" cellspacing="0">
            <tbody style="border: 1px solid #ffddc9">
                <tr>
                    <td>
                        <!-- Header -->
                        <div class="email-header">
                            System Generated Email
                        </div>

                        
                        <!-- Logo -->
                        <div class="email-logo">
                            <a href="#">
                                <img src="https://i.ibb.co.com/dLYyDXX/OVO-logo-for-Light-BG.png" alt="Company Logo">
                            </a>
                        </div>
                        <!-- Content -->
                        <div class="email-content">
                            <h1>Hello {{username}}</h1>
                            <p>{{message}}</p>
                        </div>

                        <!-- Footer -->
                        <div class="email-footer">
                            &copy; 2024 <a href="#" style="color: #0087ff;">{{site_name}}</a>. All Rights Reserved.
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>";s:12:"sms_template";s:43:"hi {{fullname}} ({{username}}), {{message}}";s:8:"sms_from";s:13:"{{site_name}}";s:10:"push_title";s:13:"{{site_name}}";s:13:"push_template";s:43:"hi {{fullname}} ({{username}}), {{message}}";s:10:"base_color";s:6:"7c4dff";s:15:"secondary_color";s:6:"cfff4d";s:11:"mail_config";s:14:"{"name":"php"}";s:10:"sms_config";s:765:"{"name":"infobip","clickatell":{"api_key":"----------------"},"infobip":{"username":"------------","password":"-----------------"},"message_bird":{"api_key":"-------------------"},"nexmo":{"api_key":"----------------------","api_secret":"----------------------"},"sms_broadcast":{"username":"----------------------","password":"-----------------------------"},"twilio":{"account_sid":"-----------------------","auth_token":"---------------------------","from":"----------------------"},"text_magic":{"username":"-----------------------","apiv2_key":"-------------------------------"},"custom":{"method":"get","url":"https:\/\/hostname.com\/demo-api-v1","headers":{"name":["api_key"],"value":["test_api 555"]},"body":{"name":["from_number"],"value":["**********"]}}}";s:15:"firebase_config";s:295:"{"apiKey":"AIzaSyD-nNargcpYyqJYb82c47dOEKR94FEzBoQ","authDomain":"swt-app-e8344.firebaseapp.com","projectId":"swt-app-e8344","storageBucket":"swt-app-e8344.firebasestorage.app","messagingSenderId":"************","appId":"1:************:web:e57f056bb612fcfa0e1df3","measurementId":"G-PJ5DJ5DWP8"}";s:17:"global_shortcodes";s:128:"{
    "site_name":"Name of your site",
    "site_currency":"Currency of your site",
    "currency_symbol":"Symbol of currency"
}";s:2:"kv";i:0;s:2:"ev";i:0;s:2:"en";i:1;s:2:"sv";i:0;s:2:"sn";i:1;s:2:"pn";i:1;s:9:"force_ssl";i:0;s:14:"in_app_payment";i:1;s:16:"maintenance_mode";i:0;s:15:"secure_password";i:0;s:5:"agree";i:1;s:14:"multi_language";i:1;s:12:"registration";i:1;s:15:"active_template";s:5:"basic";s:21:"socialite_credentials";s:602:"{"google":{"client_id":"------------","client_secret":"-------------","status":1,"info":"Quickly set up Google Login for easy and secure access to your website for all users"},"facebook":{"client_id":"------","client_secret":"sdfsdf","status":1,"info":"Set up Facebook Login for fast, secure user access and seamless integration with your website."},"linkedin":{"client_id":"-----","client_secret":"http:\/\/localhost\/flutter\/admin_panel\/user\/social-login\/callback\/linkedin","status":1,"info":"Set up LinkedIn Login for professional, secure access and easy user authentication on your website."}}";s:9:"last_cron";s:19:"2024-10-07 11:16:38";s:17:"available_version";s:3:"1.0";s:17:"system_customized";i:0;s:15:"paginate_number";i:20;s:15:"currency_format";i:1;s:11:"time_format";s:5:"h:i A";s:11:"date_format";s:5:"Y-m-d";s:15:"allow_precision";i:1;s:18:"thousand_separator";s:1:",";s:15:"google_maps_api";s:39:"AIzaSyBqQ6cxeVBunYWBIWkayW5D-hu_QFVYZ6o";s:12:"min_distance";d:1;s:13:"pusher_config";s:105:"{"app_key":"d361cec34e9f26e41029","app_id":"2015470","app_secret":"bd7b8c9230d9c2ada115","cluster":"ap2"}";s:23:"user_cancellation_limit";i:5;s:16:"ride_cancel_time";i:15;s:19:"tips_suggest_amount";s:16:"["05","10","50"]";s:17:"operating_country";s:51:"{"SA":{"country":"Saudi Arabia","dial_code":"966"}}";s:19:"driver_registration";i:1;s:23:"negative_balance_driver";s:10:"0.00000000";s:12:"google_login";i:0;s:15:"preloader_image";s:27:"6744470398af81732527875.gif";s:10:"created_at";N;s:10:"updated_at";s:19:"2025-07-01 09:50:41";}s:11:" * original";a:54:{s:2:"id";i:1;s:9:"site_name";s:3:"SWT";s:8:"cur_text";s:3:"SAR";s:7:"cur_sym";s:3:"SAR";s:10:"email_from";s:20:"<EMAIL>";s:15:"email_from_name";s:13:"{{site_name}}";s:14:"email_template";s:4270:"<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Notification</title>
    <style>
        /* General Styles */
        body {
            margin: 0;
            padding: 0;
            font-family: 'Open Sans', Arial, sans-serif;
            background-color: #f4f4f4;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        table {
            border-spacing: 0;
            border-collapse: collapse;
            width: 100%;
        }

        img {
            display: block;
            border: 0;
            line-height: 0;
        }

        a {
            color: #ff600036;
            text-decoration: none;
        }

        .email-wrapper {
            width: 100%;
            background-color: #f4f4f4;
            padding: 30px 0;
        }

        .email-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .email-header {
            background-color: #ff600036;
            color: #000;
            text-align: center;
            padding: 20px;
            font-size: 16px;
            font-weight: 600;
        }

        /* Logo */
        .email-logo {
            text-align: center;
            padding: 40px 0;
        }

        .email-logo img {
            max-width: 180px;
            margin: 0 auto;
        }

        /* Content */
        .email-content {
            padding: 0 30px 30px 30px;
            text-align: left;
        }

        .email-content h1 {
            font-size: 22px;
            color: #414a51;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .email-content p {
            font-size: 16px;
            color: #7f8c8d;
            line-height: 1.6;
            margin: 20px 0;
        }

        .email-divider {
            margin: 20px auto;
            width: 60px;
            border-bottom: 3px solid #ff600036;
        }

        /* Footer */
        .email-footer {
            background-color: #ff600036;
            color: #000;
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            padding: 20px;
        }


        /* Responsive Design */
        @media only screen and (max-width: 480px) {
            .email-content {
                padding: 20px;
            }

            .email-header,
            .email-footer {
                padding: 15px;
            }
        }
    </style>
</head>

<body>
    <div class="email-wrapper">
        <table class="email-container" cellpadding="0" cellspacing="0">
            <tbody style="border: 1px solid #ffddc9">
                <tr>
                    <td>
                        <!-- Header -->
                        <div class="email-header">
                            System Generated Email
                        </div>

                        
                        <!-- Logo -->
                        <div class="email-logo">
                            <a href="#">
                                <img src="https://i.ibb.co.com/dLYyDXX/OVO-logo-for-Light-BG.png" alt="Company Logo">
                            </a>
                        </div>
                        <!-- Content -->
                        <div class="email-content">
                            <h1>Hello {{username}}</h1>
                            <p>{{message}}</p>
                        </div>

                        <!-- Footer -->
                        <div class="email-footer">
                            &copy; 2024 <a href="#" style="color: #0087ff;">{{site_name}}</a>. All Rights Reserved.
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</body>

</html>";s:12:"sms_template";s:43:"hi {{fullname}} ({{username}}), {{message}}";s:8:"sms_from";s:13:"{{site_name}}";s:10:"push_title";s:13:"{{site_name}}";s:13:"push_template";s:43:"hi {{fullname}} ({{username}}), {{message}}";s:10:"base_color";s:6:"7c4dff";s:15:"secondary_color";s:6:"cfff4d";s:11:"mail_config";s:14:"{"name":"php"}";s:10:"sms_config";s:765:"{"name":"infobip","clickatell":{"api_key":"----------------"},"infobip":{"username":"------------","password":"-----------------"},"message_bird":{"api_key":"-------------------"},"nexmo":{"api_key":"----------------------","api_secret":"----------------------"},"sms_broadcast":{"username":"----------------------","password":"-----------------------------"},"twilio":{"account_sid":"-----------------------","auth_token":"---------------------------","from":"----------------------"},"text_magic":{"username":"-----------------------","apiv2_key":"-------------------------------"},"custom":{"method":"get","url":"https:\/\/hostname.com\/demo-api-v1","headers":{"name":["api_key"],"value":["test_api 555"]},"body":{"name":["from_number"],"value":["**********"]}}}";s:15:"firebase_config";s:295:"{"apiKey":"AIzaSyD-nNargcpYyqJYb82c47dOEKR94FEzBoQ","authDomain":"swt-app-e8344.firebaseapp.com","projectId":"swt-app-e8344","storageBucket":"swt-app-e8344.firebasestorage.app","messagingSenderId":"************","appId":"1:************:web:e57f056bb612fcfa0e1df3","measurementId":"G-PJ5DJ5DWP8"}";s:17:"global_shortcodes";s:128:"{
    "site_name":"Name of your site",
    "site_currency":"Currency of your site",
    "currency_symbol":"Symbol of currency"
}";s:2:"kv";i:0;s:2:"ev";i:0;s:2:"en";i:1;s:2:"sv";i:0;s:2:"sn";i:1;s:2:"pn";i:1;s:9:"force_ssl";i:0;s:14:"in_app_payment";i:1;s:16:"maintenance_mode";i:0;s:15:"secure_password";i:0;s:5:"agree";i:1;s:14:"multi_language";i:1;s:12:"registration";i:1;s:15:"active_template";s:5:"basic";s:21:"socialite_credentials";s:602:"{"google":{"client_id":"------------","client_secret":"-------------","status":1,"info":"Quickly set up Google Login for easy and secure access to your website for all users"},"facebook":{"client_id":"------","client_secret":"sdfsdf","status":1,"info":"Set up Facebook Login for fast, secure user access and seamless integration with your website."},"linkedin":{"client_id":"-----","client_secret":"http:\/\/localhost\/flutter\/admin_panel\/user\/social-login\/callback\/linkedin","status":1,"info":"Set up LinkedIn Login for professional, secure access and easy user authentication on your website."}}";s:9:"last_cron";s:19:"2024-10-07 11:16:38";s:17:"available_version";s:3:"1.0";s:17:"system_customized";i:0;s:15:"paginate_number";i:20;s:15:"currency_format";i:1;s:11:"time_format";s:5:"h:i A";s:11:"date_format";s:5:"Y-m-d";s:15:"allow_precision";i:1;s:18:"thousand_separator";s:1:",";s:15:"google_maps_api";s:39:"AIzaSyBqQ6cxeVBunYWBIWkayW5D-hu_QFVYZ6o";s:12:"min_distance";d:1;s:13:"pusher_config";s:105:"{"app_key":"d361cec34e9f26e41029","app_id":"2015470","app_secret":"bd7b8c9230d9c2ada115","cluster":"ap2"}";s:23:"user_cancellation_limit";i:5;s:16:"ride_cancel_time";i:15;s:19:"tips_suggest_amount";s:16:"["05","10","50"]";s:17:"operating_country";s:51:"{"SA":{"country":"Saudi Arabia","dial_code":"966"}}";s:19:"driver_registration";i:1;s:23:"negative_balance_driver";s:10:"0.00000000";s:12:"google_login";i:0;s:15:"preloader_image";s:27:"6744470398af81732527875.gif";s:10:"created_at";N;s:10:"updated_at";s:19:"2025-07-01 09:50:41";}s:10:" * changes";a:0:{}s:8:" * casts";a:8:{s:11:"mail_config";s:6:"object";s:10:"sms_config";s:6:"object";s:17:"global_shortcodes";s:6:"object";s:21:"socialite_credentials";s:6:"object";s:15:"firebase_config";s:6:"object";s:13:"pusher_config";s:6:"object";s:17:"operating_country";s:6:"object";s:19:"tips_suggest_amount";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:4:{i:0;s:14:"email_template";i:1;s:11:"mail_config";i:2;s:10:"sms_config";i:3;s:11:"system_info";}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}}