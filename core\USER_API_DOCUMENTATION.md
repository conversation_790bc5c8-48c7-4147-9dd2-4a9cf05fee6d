# User API Documentation

## 📋 Overview
This document provides comprehensive documentation for all User/Rider API endpoints in the SWT App ride-sharing platform, including BCG Corporate employee features.

**Base URL**: `https://your-domain.com/api`

**Authentication**: <PERSON><PERSON> (where required)

**Content-Type**: `application/json`

---

## 🔐 Authentication APIs

### 1. User Login
**Endpoint**: `POST /api/login`

**Description**: Authenticate user and get access token

**Parameters**:
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
}
```

**Response**:
```json
{
    "remark": "login_success",
    "status": "success",
    "message": ["Login Successful"],
    "data": {
        "user": {
            "id": 1,
            "email": "<EMAIL>",
            "employ_type": "BCG_EMPLOYEE",
            "full_name": "<PERSON>",
            "employ_id": "BCG12345",
            "phone": "+**********",
            "firstname": "<PERSON>",
            "lastname": "<PERSON><PERSON>",
            "username": "john_doe",
            "mobile": "**********",
            "country_code": "US",
            "ev": 1,
            "sv": 1,
            "profile_complete": 1,
            "image_src": "https://example.com/images/user.jpg",
            "created_at": "2024-01-01T00:00:00.000000Z",
            "updated_at": "2024-01-15T12:00:00.000000Z"
        },
        "access_token": "1|abcdef123456...",
        "token_type": "Bearer"
    }
}
```

### 2. BCG Employee Login
**Endpoint**: `POST /api/bcg-login`

**Description**: Specialized login for BCG employees

**Parameters**:
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "employ_id": "BCG12345"
}
```

**Response**:
```json
{
    "remark": "login_success",
    "status": "success",
    "message": ["BCG Employee Login Successful"],
    "data": {
        "user": {
            "id": 1,
            "email": "<EMAIL>",
            "employ_type": "BCG_EMPLOYEE",
            "full_name": "John Doe",
            "employ_id": "BCG12345",
            "phone": "+**********",
            "ev": 1,
            "sv": 1,
            "profile_complete": 1
        },
        "access_token": "1|abcdef123456...",
        "token_type": "Bearer"
    }
}
```

### 3. User Registration
**Endpoint**: `POST /api/register`

**Description**: Register new user account

**Parameters**:
```json
{
    "firstname": "John",
    "lastname": "Doe",
    "email": "<EMAIL>",
    "mobile": "**********",
    "country_code": "US",
    "password": "SecurePassword123!",
    "password_confirmation": "SecurePassword123!",
    "employ_type": "BCG_EMPLOYEE",
    "full_name": "John Doe",
    "employ_id": "BCG12345",
    "phone": "+**********"
}
```

**Response**:
```json
{
    "remark": "registration_success",
    "status": "success",
    "message": ["Registration successful"],
    "data": {
        "user": {
            "id": 1,
            "email": "<EMAIL>",
            "firstname": "John",
            "lastname": "Doe",
            "employ_type": "BCG_EMPLOYEE",
            "full_name": "John Doe",
            "employ_id": "BCG12345"
        },
        "access_token": "1|abcdef123456...",
        "token_type": "Bearer"
    }
}
```

### 4. Forgot Password
**Endpoint**: `POST /api/password/email`

**Description**: Send password reset code to email

**Parameters**:
```json
{
    "email": "<EMAIL>"
}
```

**Response**:
```json
{
    "remark": "code_sent",
    "status": "success",
    "message": ["Password reset code sent to your email"],
    "data": null
}
```

### 5. Verify Reset Code
**Endpoint**: `POST /api/password/verify-code`

**Description**: Verify password reset code

**Parameters**:
```json
{
    "email": "<EMAIL>",
    "code": "123456"
}
```

**Response**:
```json
{
    "remark": "code_verified",
    "status": "success",
    "message": ["Code verified successfully"],
    "data": {
        "token": "reset_token_here"
    }
}
```

### 6. Reset Password
**Endpoint**: `POST /api/password/reset`

**Description**: Reset password with verified token

**Parameters**:
```json
{
    "token": "reset_token_here",
    "email": "<EMAIL>",
    "password": "NewPassword123!",
    "password_confirmation": "NewPassword123!"
}
```

**Response**:
```json
{
    "remark": "password_reset",
    "status": "success",
    "message": ["Password reset successfully"],
    "data": null
}
```

### 7. Logout
**Endpoint**: `GET /api/logout`

**Description**: Logout user and revoke token

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "logout_success",
    "status": "success",
    "message": ["Logged out successfully"],
    "data": null
}
```

---

## 👤 User Profile APIs

### 8. Get User Dashboard
**Endpoint**: `GET /api/dashboard`

**Description**: Get user dashboard data

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "dashboard_data",
    "status": "success",
    "message": ["Dashboard data retrieved"],
    "data": {
        "user": {
            "id": 1,
            "firstname": "John",
            "lastname": "Doe",
            "email": "<EMAIL>",
            "balance": "150.00",
            "total_rides": 25,
            "completed_rides": 23,
            "canceled_rides": 2
        },
        "recent_rides": [
            {
                "id": 1,
                "pickup_location": "123 Main St",
                "destination": "456 Oak Ave",
                "amount": "25.50",
                "status": "completed",
                "created_at": "2024-01-15T10:30:00Z"
            }
        ],
        "statistics": {
            "total_spent": "1250.75",
            "average_rating": "4.8",
            "favorite_driver": "Mike Johnson"
        }
    }
}
```

### 9. Get User Info
**Endpoint**: `GET /api/user-info`

**Description**: Get detailed user information

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "user_info",
    "status": "success",
    "message": ["User information retrieved"],
    "data": {
        "user": {
            "id": 1,
            "firstname": "John",
            "lastname": "Doe",
            "email": "<EMAIL>",
            "mobile": "**********",
            "country_code": "US",
            "employ_type": "BCG_EMPLOYEE",
            "full_name": "John Doe",
            "employ_id": "BCG12345",
            "phone": "+**********",
            "address": "123 Main Street",
            "city": "New York",
            "state": "NY",
            "zip": "10001",
            "country": "United States",
            "image": "user_image.jpg",
            "balance": "150.00",
            "ev": 1,
            "sv": 1,
            "profile_complete": 1
        }
    }
}
```

### 10. Update Profile
**Endpoint**: `POST /api/profile-setting`

**Description**: Update user profile information

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "firstname": "John",
    "lastname": "Doe",
    "mobile": "**********",
    "country_code": "US",
    "address": "123 Main Street",
    "city": "New York",
    "state": "NY",
    "zip": "10001",
    "country": "United States",
    "image": "base64_image_data"
}
```

**Response**:
```json
{
    "remark": "profile_updated",
    "status": "success",
    "message": ["Profile updated successfully"],
    "data": {
        "user": {
            "id": 1,
            "firstname": "John",
            "lastname": "Doe",
            "email": "<EMAIL>",
            "mobile": "**********",
            "address": "123 Main Street",
            "city": "New York",
            "state": "NY",
            "zip": "10001",
            "country": "United States",
            "updated_at": "2024-01-15T12:00:00Z"
        }
    }
}
```

### 11. Update BCG Profile
**Endpoint**: `POST /api/bcg-profile-update`

**Description**: Update BCG employee specific profile fields

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "employ_type": "BCG_EMPLOYEE",
    "full_name": "John Doe",
    "employ_id": "BCG12345",
    "phone": "+**********"
}
```

**Response**:
```json
{
    "remark": "bcg_profile_updated",
    "status": "success",
    "message": ["BCG profile updated successfully"],
    "data": {
        "user": {
            "id": 1,
            "employ_type": "BCG_EMPLOYEE",
            "full_name": "John Doe",
            "employ_id": "BCG12345",
            "phone": "+**********",
            "updated_at": "2024-01-15T12:00:00Z"
        }
    }
}
```

### 12. Change Password
**Endpoint**: `POST /api/change-password`

**Description**: Change user password

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "current_password": "CurrentPassword123!",
    "password": "NewPassword123!",
    "password_confirmation": "NewPassword123!"
}
```

**Response**:
```json
{
    "remark": "password_changed",
    "status": "success",
    "message": ["Password changed successfully"],
    "data": null
}
```

---

## 🚗 Ride Management APIs

### 13. Get Available Drivers (BCG Corporate)
**Endpoint**: `POST /api/ride/available-drivers`

**Description**: Get available drivers and fare estimate for BCG corporate rides

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "service_id": 1,
    "pickup_latitude": 40.7128,
    "pickup_longitude": -74.0060,
    "destination_latitude": 40.7589,
    "destination_longitude": -73.9851,
    "ride_option": "go_now",
    "scheduled_time": "2024-01-15T14:30:00Z",
    "stops": [
        {
            "latitude": 40.7505,
            "longitude": -73.9934,
            "address": "Times Square",
            "note": "Pick up documents"
        }
    ]
}
```

**Response**:
```json
{
    "remark": "drivers_available",
    "status": "success",
    "message": ["Available drivers found"],
    "data": {
        "driver": {
            "id": 1,
            "firstname": "Mike",
            "lastname": "Johnson",
            "mobile": "+**********",
            "avg_rating": "4.8",
            "total_rides": 150,
            "vehicle": {
                "model": "Toyota Camry",
                "color": "White",
                "year": "2022",
                "number": "ABC123"
            }
        },
        "fare_estimate": {
            "estimated_duration_minutes": 45,
            "cycles_count": 2,
            "fare_per_cycle": "25.00",
            "estimated_fare": "50.00",
            "max_duration_hours": 24,
            "max_cycles": 48,
            "cancellation_charges": "5.00",
            "billing_note": "Charged per 30-minute cycle. Company pays directly."
        },
        "pickup_zone": {
            "id": 1,
            "name": "Downtown Manhattan"
        }
    }
}
```

### 14. Create Ride (BCG Corporate)
**Endpoint**: `POST /api/ride/create`

**Description**: Create a new ride request with BCG corporate features

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "service_id": 1,
    "pickup_location": "123 Main Street, New York, NY",
    "pickup_latitude": 40.7128,
    "pickup_longitude": -74.0060,
    "destination": "456 Oak Avenue, New York, NY",
    "destination_latitude": 40.7589,
    "destination_longitude": -73.9851,
    "ride_option": "schedule",
    "scheduled_time": "2024-01-15T14:30:00Z",
    "estimated_duration": 45,
    "case_code": "BCG-CASE-2024-001",
    "stops": [
        {
            "latitude": 40.7505,
            "longitude": -73.9934,
            "address": "Times Square, New York, NY",
            "note": "Pick up documents"
        }
    ],
    "note": "Corporate meeting transportation",
    "number_of_passenger": 2
}
```

**Response**:
```json
{
    "remark": "ride_created",
    "status": "success",
    "message": ["Ride created successfully"],
    "data": {
        "ride": {
            "id": 1,
            "uid": "RIDE-2024-001",
            "user_id": 1,
            "driver_id": 5,
            "service_id": 1,
            "pickup_location": "123 Main Street, New York, NY",
            "destination": "456 Oak Avenue, New York, NY",
            "ride_option": "schedule",
            "scheduled_time": "2024-01-15T14:30:00Z",
            "estimated_duration": 45,
            "case_code": "BCG-CASE-2024-001",
            "cycles_count": 2,
            "fare_per_cycle": "25.00",
            "amount": "50.00",
            "status": "pending",
            "created_at": "2024-01-15T10:00:00Z"
        },
        "driver": {
            "id": 5,
            "firstname": "Mike",
            "lastname": "Johnson",
            "mobile": "+**********",
            "vehicle": {
                "model": "Toyota Camry",
                "color": "White",
                "number": "ABC123"
            }
        }
    }
}
```

### 15. Get Ride List
**Endpoint**: `GET /api/ride/list`

**Description**: Get user's ride history with pagination

**Headers**: `Authorization: Bearer {token}`

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (pending, running, completed, canceled)

**Response**:
```json
{
    "remark": "ride_list",
    "status": "success",
    "message": ["Ride list retrieved"],
    "data": {
        "rides": [
            {
                "id": 1,
                "uid": "RIDE-2024-001",
                "pickup_location": "123 Main Street",
                "destination": "456 Oak Avenue",
                "ride_option": "go_now",
                "amount": "25.50",
                "status": "completed",
                "driver": {
                    "firstname": "Mike",
                    "lastname": "Johnson",
                    "mobile": "+**********"
                },
                "created_at": "2024-01-15T10:00:00Z",
                "started_at": "2024-01-15T10:15:00Z",
                "ended_at": "2024-01-15T10:45:00Z"
            }
        ],
        "pagination": {
            "current_page": 1,
            "total_pages": 5,
            "total_items": 50,
            "per_page": 10
        }
    }
}
```

### 16. Get Ride Details
**Endpoint**: `GET /api/ride/details/{ride_id}`

**Description**: Get detailed information about a specific ride

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "ride_details",
    "status": "success",
    "message": ["Ride details retrieved"],
    "data": {
        "ride": {
            "id": 1,
            "uid": "RIDE-2024-001",
            "pickup_location": "123 Main Street, New York, NY",
            "destination": "456 Oak Avenue, New York, NY",
            "ride_option": "schedule",
            "scheduled_time": "2024-01-15T14:30:00Z",
            "estimated_duration": 45,
            "case_code": "BCG-CASE-2024-001",
            "stops": [
                {
                    "latitude": 40.7505,
                    "longitude": -73.9934,
                    "address": "Times Square",
                    "note": "Pick up documents"
                }
            ],
            "cycles_count": 2,
            "fare_per_cycle": "25.00",
            "amount": "50.00",
            "payment_type": "company",
            "status": "completed",
            "note": "Corporate meeting transportation",
            "created_at": "2024-01-15T10:00:00Z",
            "started_at": "2024-01-15T14:30:00Z",
            "ended_at": "2024-01-15T15:15:00Z"
        },
        "driver": {
            "id": 5,
            "firstname": "Mike",
            "lastname": "Johnson",
            "mobile": "+**********",
            "avg_rating": "4.8",
            "vehicle": {
                "model": "Toyota Camry",
                "color": "White",
                "year": "2022",
                "number": "ABC123"
            }
        },
        "service": {
            "id": 1,
            "name": "Car",
            "image": "car_icon.png"
        }
    }
}
```

### 17. Cancel Ride
**Endpoint**: `POST /api/ride/cancel/{ride_id}`

**Description**: Cancel a pending or scheduled ride

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "reason": "Meeting canceled"
}
```

**Response**:
```json
{
    "remark": "ride_canceled",
    "status": "success",
    "message": ["Ride canceled successfully"],
    "data": {
        "ride": {
            "id": 1,
            "status": "canceled",
            "canceled_at": "2024-01-15T12:00:00Z",
            "canceled_reason": "Meeting canceled",
            "cancellation_charges": "5.00"
        }
    }
}
```

---

## 🎫 Coupon APIs

### 18. Get Available Coupons
**Endpoint**: `GET /api/coupons`

**Description**: Get list of available coupons for user

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "coupons_list",
    "status": "success",
    "message": ["Available coupons retrieved"],
    "data": {
        "coupons": [
            {
                "id": 1,
                "title": "First Ride Discount",
                "code": "FIRST20",
                "discount": "20.00",
                "discount_type": "percentage",
                "minimum_amount": "10.00",
                "maximum_discount": "50.00",
                "valid_until": "2024-12-31T23:59:59Z",
                "usage_limit": 1,
                "used_count": 0
            }
        ]
    }
}
```

### 19. Apply Coupon
**Endpoint**: `POST /api/apply-coupon/{ride_id}`

**Description**: Apply coupon to a ride

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "coupon_code": "FIRST20"
}
```

**Response**:
```json
{
    "remark": "coupon_applied",
    "status": "success",
    "message": ["Coupon applied successfully"],
    "data": {
        "ride": {
            "id": 1,
            "original_amount": "50.00",
            "discount_amount": "10.00",
            "final_amount": "40.00"
        },
        "coupon": {
            "code": "FIRST20",
            "discount": "20.00",
            "discount_type": "percentage"
        }
    }
}
```

### 20. Remove Coupon
**Endpoint**: `POST /api/remove-coupon/{ride_id}`

**Description**: Remove applied coupon from ride

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "coupon_removed",
    "status": "success",
    "message": ["Coupon removed successfully"],
    "data": {
        "ride": {
            "id": 1,
            "amount": "50.00"
        }
    }
}
```

---

## ⭐ Review APIs

### 21. Submit Review
**Endpoint**: `POST /api/review/{ride_id}`

**Description**: Submit review for completed ride

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "rating": 5,
    "comment": "Excellent service! Very professional driver."
}
```

**Response**:
```json
{
    "remark": "review_submitted",
    "status": "success",
    "message": ["Review submitted successfully"],
    "data": {
        "review": {
            "id": 1,
            "ride_id": 1,
            "driver_id": 5,
            "rating": 5,
            "comment": "Excellent service! Very professional driver.",
            "created_at": "2024-01-15T16:30:00Z"
        }
    }
}
```

### 22. Get Driver Reviews
**Endpoint**: `GET /api/get-driver-review/{driver_id}`

**Description**: Get reviews for a specific driver

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "driver_reviews",
    "status": "success",
    "message": ["Driver reviews retrieved"],
    "data": {
        "driver": {
            "id": 5,
            "firstname": "Mike",
            "lastname": "Johnson",
            "avg_rating": "4.8",
            "total_reviews": 25
        },
        "reviews": [
            {
                "id": 1,
                "rating": 5,
                "comment": "Excellent service!",
                "user": {
                    "firstname": "John",
                    "lastname": "D."
                },
                "created_at": "2024-01-15T16:30:00Z"
            }
        ]
    }
}
```

---

## 💬 Messaging APIs

### 23. Get Ride Messages
**Endpoint**: `GET /api/ride/messages/{ride_id}`

**Description**: Get chat messages for a ride

**Headers**: `Authorization: Bearer {token}`

**Response**:
```json
{
    "remark": "messages_retrieved",
    "status": "success",
    "message": ["Messages retrieved successfully"],
    "data": {
        "messages": [
            {
                "id": 1,
                "ride_id": 1,
                "sender_type": "user",
                "sender_id": 1,
                "message": "I'm waiting at the pickup location",
                "created_at": "2024-01-15T14:25:00Z"
            },
            {
                "id": 2,
                "ride_id": 1,
                "sender_type": "driver",
                "sender_id": 5,
                "message": "I'll be there in 2 minutes",
                "created_at": "2024-01-15T14:26:00Z"
            }
        ]
    }
}
```

### 24. Send Message
**Endpoint**: `POST /api/ride/send/message/{ride_id}`

**Description**: Send message to driver during ride

**Headers**: `Authorization: Bearer {token}`

**Parameters**:
```json
{
    "message": "I'm running 5 minutes late"
}
```

**Response**:
```json
{
    "remark": "message_sent",
    "status": "success",
    "message": ["Message sent successfully"],
    "data": {
        "message": {
            "id": 3,
            "ride_id": 1,
            "sender_type": "user",
            "sender_id": 1,
            "message": "I'm running 5 minutes late",
            "created_at": "2024-01-15T14:27:00Z"
        }
    }
}
```
