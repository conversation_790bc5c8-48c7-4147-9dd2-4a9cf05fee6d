<?php
/**
 * Test script for debugging image upload to profile-setting endpoint
 * 
 * Usage: 
 * 1. Replace YOUR_AUTH_TOKEN with actual token
 * 2. Replace /path/to/test/image.jpg with actual image path
 * 3. Run: php test_image_upload.php
 */

// Configuration
$baseUrl = 'http://localhost/swt_app/api';
$authToken = 'YOUR_AUTH_TOKEN'; // Replace with actual token
$imagePath = '/path/to/test/image.jpg'; // Replace with actual image path

// Test 1: Using 'image' field name (correct)
echo "=== Test 1: Using 'image' field name ===\n";
testImageUpload($baseUrl, $authToken, $imagePath, 'image');

echo "\n";

// Test 2: Using 'image_src' field name (now supported)
echo "=== Test 2: Using 'image_src' field name ===\n";
testImageUpload($baseUrl, $authToken, $imagePath, 'image_src');

function testImageUpload($baseUrl, $authToken, $imagePath, $fieldName) {
    // Check if image file exists
    if (!file_exists($imagePath)) {
        echo "❌ Error: Image file not found at: $imagePath\n";
        return;
    }

    // Prepare cURL
    $ch = curl_init();
    
    // Create CURLFile for image upload
    $imageFile = new CURLFile($imagePath, mime_content_type($imagePath), basename($imagePath));
    
    // Prepare form data
    $postData = [
        $fieldName => $imageFile,
        'full_name' => 'Test User Updated',
        'phone' => '+1234567890'
    ];

    // Set cURL options
    curl_setopt_array($ch, [
        CURLOPT_URL => $baseUrl . '/profile-setting',
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $postData,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $authToken,
            'Accept: application/json'
        ],
        CURLOPT_VERBOSE => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30
    ]);

    // Execute request
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);

    // Display results
    echo "Field name used: '$fieldName'\n";
    echo "HTTP Status: $httpCode\n";
    
    if ($error) {
        echo "❌ cURL Error: $error\n";
        return;
    }

    if ($response) {
        $data = json_decode($response, true);
        
        if ($httpCode === 200) {
            echo "✅ Success!\n";
            if (isset($data['data']['user']['image_src'])) {
                echo "📷 New image_src: " . $data['data']['user']['image_src'] . "\n";
            }
        } else {
            echo "❌ Failed!\n";
        }
        
        echo "Response: " . json_encode($data, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "❌ No response received\n";
    }
}

// Helper function to get user info
function getUserInfo($baseUrl, $authToken) {
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $baseUrl . '/user-info',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $authToken,
            'Accept: application/json'
        ]
    ]);

    $response = curl_exec($ch);
    curl_close($ch);

    return json_decode($response, true);
}

echo "\n=== Getting current user info ===\n";
$userInfo = getUserInfo($baseUrl, $authToken);
if ($userInfo && isset($userInfo['data']['user']['image_src'])) {
    echo "Current image_src: " . $userInfo['data']['user']['image_src'] . "\n";
} else {
    echo "Could not retrieve user info or no image_src found\n";
}
?>
