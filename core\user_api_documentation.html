<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User API Documentation - SWT App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav h3 {
            margin-top: 0;
            color: #333;
        }

        .nav ul {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .nav li {
            margin: 5px 0;
        }

        .nav a {
            color: #667eea;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            display: block;
            transition: background-color 0.3s;
        }

        .nav a:hover {
            background-color: #f0f2ff;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            background: #667eea;
            color: white;
            padding: 20px;
            margin: 0;
        }

        .section-content {
            padding: 30px;
        }

        .endpoint {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
            margin-right: 10px;
        }

        .method.get {
            background: #d4edda;
            color: #155724;
        }

        .method.post {
            background: #d1ecf1;
            color: #0c5460;
        }

        .method.put {
            background: #fff3cd;
            color: #856404;
        }

        .method.delete {
            background: #f8d7da;
            color: #721c24;
        }

        .endpoint-url {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            color: #333;
        }

        .endpoint-content {
            padding: 20px;
        }

        .description {
            margin-bottom: 20px;
            color: #666;
        }

        .params-section,
        .response-section {
            margin-bottom: 25px;
        }

        .params-section h4,
        .response-section h4 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #667eea;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .param-table th,
        .param-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .param-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .param-table .param-name {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #667eea;
        }

        .param-table .param-type {
            color: #28a745;
            font-style: italic;
        }

        .param-table .required {
            color: #dc3545;
            font-weight: bold;
        }

        .param-table .optional {
            color: #6c757d;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .json {
            color: #333;
        }

        .json .key {
            color: #d73a49;
        }

        .json .string {
            color: #032f62;
        }

        .json .number {
            color: #005cc5;
        }

        .json .boolean {
            color: #e36209;
        }

        .status-code {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
        }

        .status-200 {
            background: #d4edda;
            color: #155724;
        }

        .status-400 {
            background: #fff3cd;
            color: #856404;
        }

        .status-401 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-404 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-422 {
            background: #fff3cd;
            color: #856404;
        }

        .status-500 {
            background: #f8d7da;
            color: #721c24;
        }

        .note {
            background: #e7f3ff;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .note h5 {
            margin-top: 0;
            color: #667eea;
        }

        .auth-required {
            background: #fff3cd;
            color: #856404;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.9em;
            margin-bottom: 15px;
            display: inline-block;
        }

        .toc {
            position: sticky;
            top: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .nav ul {
                grid-template-columns: 1fr;
            }

            .param-table {
                font-size: 0.9em;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚗 User API Documentation</h1>
            <p>SWT App - Ride Sharing Platform for Users & BCG Employees</p>
            <p><strong>Base URL:</strong> <code>https://your-domain.com/api</code></p>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#authentication">🔐 Authentication</a></li>
                <li><a href="#authorization">✅ Authorization</a></li>
                <li><a href="#profile">👤 Profile Management</a></li>
                <li><a href="#rides">🚗 Ride Management</a></li>
                <li><a href="#coupons">🎫 Coupons</a></li>
                <li><a href="#reviews">⭐ Reviews</a></li>
                <li><a href="#support">🎧 Support</a></li>
                <li><a href="#messages">💬 Messages</a></li>
            </ul>
        </div>

        <!-- Authentication Section -->
        <div class="section" id="authentication">
            <h2 class="section-header">🔐 Authentication</h2>
            <div class="section-content">

                <!-- User Login -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/user/login</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Standard user login for regular customers and BCG employees.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>User's email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>User's password</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "login_success",
  "status": "success",
  "message": {
    "success": ["Login Successful"]
  },
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "employ_type": "BCG_EMPLOYEE",
      "full_name": "John Doe",
      "employ_id": "BCG12345",
      "phone": "+1234567890",
      "firstname": "John",
      "lastname": "Doe",
      "username": "johndoe",
      "mobile": "1234567890",
      "country_code": "US",
      "ev": 1,
      "sv": 1,
      "profile_complete": 1,
      "image_src": "https://example.com/image.jpg",
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "access_token": "1|abcdef123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>

                            <p><span class="status-code status-422">422</span> Validation Error</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "validation_error",
  "status": "error",
  "message": {
    "error": ["The email field is required.", "The password field is required."]
  }
}</pre>
                            </div>

                            <p><span class="status-code status-401">401</span> Invalid Credentials</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "invalid_credential",
  "status": "error",
  "message": {
    "error": ["Invalid email or password"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- BCG Login -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/user/bcg-login</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Specialized login endpoint for BCG employees with enhanced authentication.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>BCG employee email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Employee password</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_id</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>BCG employee ID for verification</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "bcg_login_success",
  "status": "success",
  "message": {
    "success": ["BCG Employee Login Successful"]
  },
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "employ_type": "BCG_EMPLOYEE",
      "full_name": "Jane Smith",
      "employ_id": "BCG67890",
      "phone": "+1987654321",
      "profile_complete": 1,
      "corporate_privileges": true
    },
    "access_token": "1|bcgtoken123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Registration -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/user/register</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Register a new user account. Supports both regular users and BCG employees.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">firstname</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>User's first name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">lastname</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>User's last name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Valid email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Password (min 6 characters)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password_confirmation</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Password confirmation</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_type</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Employee type (BCG_EMPLOYEE, REGULAR)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">full_name</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Full name for BCG employees</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_id</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>BCG employee ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">phone</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Phone number with country code</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "registration_success",
  "status": "success",
  "message": {
    "success": ["Registration successful"]
  },
  "data": {
    "user": {
      "id": 2,
      "email": "<EMAIL>",
      "employ_type": "REGULAR",
      "profile_complete": 0,
      "ev": 0,
      "sv": 0
    },
    "access_token": "2|newtoken123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ride Management Section -->
        <div class="section" id="rides">
            <h2 class="section-header">🚗 Ride Management</h2>
            <div class="section-content">

                <!-- Get Available Drivers -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/available-drivers</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get available drivers and fare estimate for BCG Corporate rides. Supports both immediate and
                            scheduled rides with multiple stops.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">service_id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Service type ID (car, bike, etc.)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup location latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup location longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">ride_option</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Either "go_now" or "schedule"</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">scheduled_time</td>
                                        <td class="param-type">datetime</td>
                                        <td class="optional">Required if ride_option=schedule</td>
                                        <td>Scheduled time (min 2 hours from now)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops</td>
                                        <td class="param-type">array</td>
                                        <td class="optional">Optional</td>
                                        <td>Array of stop locations</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Required with stops</td>
                                        <td>Stop latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Required with stops</td>
                                        <td>Stop longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.address</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Stop address description</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.note</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Special instructions for stop</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "available_drivers_found",
  "status": "success",
  "message": {
    "success": ["Available drivers found"]
  },
  "data": {
    "available_driver": {
      "id": 1,
      "firstname": "John",
      "lastname": "Driver",
      "mobile": "1234567890",
      "avg_rating": "4.85",
      "total_rides": 150,
      "vehicle": {
        "model": "Toyota Camry",
        "color": "White",
        "year": "2022",
        "vehicle_number": "ABC123"
      }
    },
    "fare_data": {
      "estimated_duration_minutes": 45,
      "cycles_count": 2,
      "fare_per_cycle": "25.00",
      "estimated_fare": "50.00",
      "max_duration_hours": 24,
      "max_cycles": 48,
      "cancellation_charges": "5.00",
      "billing_note": "Charged per 30-minute cycle. Company pays directly."
    },
    "pickup_zone": {
      "id": 1,
      "name": "Downtown",
      "status": 1
    },
    "service": {
      "id": 1,
      "name": "Car",
      "half_hour_fare": "25.00"
    }
  }
}</pre>
                            </div>

                            <p><span class="status-code status-404">404</span> No Drivers Available</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "not_found",
  "status": "error",
  "message": {
    "error": ["No drivers available in this area"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Ride -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/create</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Create a new ride request. BCG Corporate system automatically assigns the best available
                            driver.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">service_id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Service type ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_location</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Pickup address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">ride_option</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>"go_now" or "schedule"</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">scheduled_time</td>
                                        <td class="param-type">datetime</td>
                                        <td class="optional">Required if scheduled</td>
                                        <td>Scheduled pickup time</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">estimated_duration</td>
                                        <td class="param-type">integer</td>
                                        <td class="optional">Optional</td>
                                        <td>Estimated duration in minutes</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">case_code</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>BCG case code for billing</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops</td>
                                        <td class="param-type">array</td>
                                        <td class="optional">Optional</td>
                                        <td>Multiple stops data</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">note</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Special instructions</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">number_of_passenger</td>
                                        <td class="param-type">integer</td>
                                        <td class="optional">Optional</td>
                                        <td>Number of passengers (default: 1)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_created_success",
  "status": "success",
  "message": {
    "success": ["Ride created successfully"]
  },
  "data": {
    "ride": {
      "id": 123,
      "uid": "RIDE123456",
      "pickup_location": "123 Main St",
      "destination": "456 Oak Ave",
      "ride_option": "go_now",
      "estimated_duration": 30,
      "cycles_count": 1,
      "fare_per_cycle": "25.00",
      "amount": "25.00",
      "case_code": "BCG2024001",
      "status": 0,
      "driver": {
        "id": 1,
        "name": "John Driver",
        "mobile": "1234567890",
        "vehicle": "Toyota Camry - ABC123"
      },
      "created_at": "2024-01-01T10:00:00.000000Z"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Management Section -->
        <div class="section" id="profile">
            <h2 class="section-header">👤 Profile Management</h2>
            <div class="section-content">

                <!-- Get User Info -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/user-info</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get current user information including BCG employee details.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "user_info",
  "status": "success",
  "message": {
    "success": ["User information retrieved"]
  },
  "data": {
    "user": {
      "id": 1,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "employ_type": "BCG_EMPLOYEE",
      "full_name": "John Doe",
      "employ_id": "BCG12345",
      "phone": "+1234567890",
      "username": "johndoe",
      "mobile": "1234567890",
      "country_code": "US",
      "profile_complete": 1,
      "ev": 1,
      "sv": 1,
      "image_src": "https://example.com/image.jpg"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update BCG Profile -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/bcg-profile-update</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Update BCG employee profile information.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">full_name</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Full name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_id</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Employee ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">phone</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Phone number</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">image</td>
                                        <td class="param-type">file</td>
                                        <td class="optional">Optional</td>
                                        <td>Profile image</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "profile_updated",
  "status": "success",
  "message": {
    "success": ["Profile updated successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="note">
            <h5>📝 Important Notes</h5>
            <ul>
                <li><strong>Authentication:</strong> All protected endpoints require Bearer token in Authorization
                    header</li>
                <li><strong>BCG Corporate:</strong> Special features available for BCG employees including direct
                    billing and enhanced ride options</li>
                <li><strong>Rate Limiting:</strong> API calls are rate limited to prevent abuse</li>
                <li><strong>Error Handling:</strong> All errors follow consistent format with remark, status, and
                    message fields</li>
                <li><strong>Timestamps:</strong> All timestamps are in UTC format</li>
            </ul>
        </div>

        <div class="section">
            <h2 class="section-header">📞 Support & Contact</h2>
            <div class="section-content">
                <p>For technical support or API questions, please contact:</p>
                <ul>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Documentation:</strong> <a href="#" target="_blank">Developer Portal</a></li>
                    <li><strong>Status Page:</strong> <a href="#" target="_blank">API Status</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>

</html>