<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User API Documentation - SWT App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav h3 {
            margin-top: 0;
            color: #333;
        }

        .nav ul {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .nav li {
            margin: 5px 0;
        }

        .nav a {
            color: #667eea;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            display: block;
            transition: background-color 0.3s;
        }

        .nav a:hover {
            background-color: #f0f2ff;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            background: #667eea;
            color: white;
            padding: 20px;
            margin: 0;
        }

        .section-content {
            padding: 30px;
        }

        .endpoint {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
            margin-right: 10px;
        }

        .method.get {
            background: #d4edda;
            color: #155724;
        }

        .method.post {
            background: #d1ecf1;
            color: #0c5460;
        }

        .method.put {
            background: #fff3cd;
            color: #856404;
        }

        .method.delete {
            background: #f8d7da;
            color: #721c24;
        }

        .endpoint-url {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            color: #333;
        }

        .endpoint-content {
            padding: 20px;
        }

        .description {
            margin-bottom: 20px;
            color: #666;
        }

        .params-section,
        .response-section {
            margin-bottom: 25px;
        }

        .params-section h4,
        .response-section h4 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #667eea;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .param-table th,
        .param-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .param-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .param-table .param-name {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #667eea;
        }

        .param-table .param-type {
            color: #28a745;
            font-style: italic;
        }

        .param-table .required {
            color: #dc3545;
            font-weight: bold;
        }

        .param-table .optional {
            color: #6c757d;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .json {
            color: #333;
        }

        .json .key {
            color: #d73a49;
        }

        .json .string {
            color: #032f62;
        }

        .json .number {
            color: #005cc5;
        }

        .json .boolean {
            color: #e36209;
        }

        .status-code {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
        }

        .status-200 {
            background: #d4edda;
            color: #155724;
        }

        .status-400 {
            background: #fff3cd;
            color: #856404;
        }

        .status-401 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-404 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-422 {
            background: #fff3cd;
            color: #856404;
        }

        .status-500 {
            background: #f8d7da;
            color: #721c24;
        }

        .note {
            background: #e7f3ff;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .note h5 {
            margin-top: 0;
            color: #667eea;
        }

        .auth-required {
            background: #fff3cd;
            color: #856404;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.9em;
            margin-bottom: 15px;
            display: inline-block;
        }

        .toc {
            position: sticky;
            top: 20px;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .nav ul {
                grid-template-columns: 1fr;
            }

            .param-table {
                font-size: 0.9em;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚗 User API Documentation</h1>
            <p>SWT App - Ride Sharing Platform for Users & BCG Employees</p>
            <p><strong>Base URL:</strong> <code>https://your-domain.com/api</code></p>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#authentication">🔐 Authentication</a></li>
                <li><a href="#authorization">✅ Authorization</a></li>
                <li><a href="#profile">👤 Profile Management</a></li>
                <li><a href="#rides">🚗 Ride Management</a></li>
                <li><a href="#coupons">🎫 Coupons</a></li>
                <li><a href="#reviews">⭐ Reviews</a></li>
                <li><a href="#support">🎧 Support</a></li>
                <li><a href="#messages">💬 Messages</a></li>
            </ul>
        </div>

        <!-- Authentication Section -->
        <div class="section" id="authentication">
            <h2 class="section-header">🔐 Authentication</h2>
            <div class="section-content">

                <!-- User Login -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/user/login</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Standard user login for regular customers and BCG employees.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>User's email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>User's password</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "login_success",
  "status": "success",
  "message": {
    "success": ["Login Successful"]
  },
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "employ_type": "BCG_EMPLOYEE",
      "full_name": "John Doe",
      "employ_id": "BCG12345",
      "phone": "+1234567890",
      "firstname": "John",
      "lastname": "Doe",
      "username": "johndoe",
      "mobile": "1234567890",
      "country_code": "US",
      "ev": 1,
      "sv": 1,
      "profile_complete": 1,
      "image_src": "https://example.com/image.jpg",
      "created_at": "2024-01-01T00:00:00.000000Z",
      "updated_at": "2024-01-01T00:00:00.000000Z"
    },
    "access_token": "1|abcdef123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>

                            <p><span class="status-code status-422">422</span> Validation Error</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "validation_error",
  "status": "error",
  "message": {
    "error": ["The email field is required.", "The password field is required."]
  }
}</pre>
                            </div>

                            <p><span class="status-code status-401">401</span> Invalid Credentials</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "invalid_credential",
  "status": "error",
  "message": {
    "error": ["Invalid email or password"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- BCG Login -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/user/bcg-login</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Specialized login endpoint for BCG employees with enhanced authentication.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>BCG employee email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Employee password</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_id</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>BCG employee ID for verification</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "bcg_login_success",
  "status": "success",
  "message": {
    "success": ["BCG Employee Login Successful"]
  },
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "employ_type": "BCG_EMPLOYEE",
      "full_name": "Jane Smith",
      "employ_id": "BCG67890",
      "phone": "+1987654321",
      "profile_complete": 1,
      "corporate_privileges": true
    },
    "access_token": "1|bcgtoken123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Registration -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/user/register</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Register a new user account. Supports both regular users and BCG employees.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">firstname</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>User's first name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">lastname</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>User's last name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Valid email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Password (min 6 characters)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password_confirmation</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Password confirmation</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_type</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Employee type (BCG_EMPLOYEE, REGULAR)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">full_name</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Full name for BCG employees</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_id</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>BCG employee ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">phone</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Phone number with country code</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "registration_success",
  "status": "success",
  "message": {
    "success": ["Registration successful"]
  },
  "data": {
    "user": {
      "id": 2,
      "email": "<EMAIL>",
      "employ_type": "REGULAR",
      "profile_complete": 0,
      "ev": 0,
      "sv": 0
    },
    "access_token": "2|newtoken123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ride Management Section -->
        <div class="section" id="rides">
            <h2 class="section-header">🚗 Ride Management</h2>
            <div class="section-content">

                <!-- Get Available Drivers -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/available-drivers</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get available drivers and fare estimate for BCG Corporate rides. Supports both immediate and
                            scheduled rides with multiple stops.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">service_id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Service type ID (car, bike, etc.)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup location latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup location longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">ride_option</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Either "go_now" or "schedule"</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">scheduled_time</td>
                                        <td class="param-type">datetime</td>
                                        <td class="optional">Required if ride_option=schedule</td>
                                        <td>Scheduled time (min 2 hours from now)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops</td>
                                        <td class="param-type">array</td>
                                        <td class="optional">Optional</td>
                                        <td>Array of stop locations</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Required with stops</td>
                                        <td>Stop latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Required with stops</td>
                                        <td>Stop longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.address</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Stop address description</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops.*.note</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Special instructions for stop</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "available_drivers_found",
  "status": "success",
  "message": {
    "success": ["Available drivers found"]
  },
  "data": {
    "available_driver": {
      "id": 1,
      "firstname": "John",
      "lastname": "Driver",
      "mobile": "1234567890",
      "avg_rating": "4.85",
      "total_rides": 150,
      "vehicle": {
        "model": "Toyota Camry",
        "color": "White",
        "year": "2022",
        "vehicle_number": "ABC123"
      }
    },
    "fare_data": {
      "estimated_duration_minutes": 45,
      "cycles_count": 2,
      "fare_per_cycle": "25.00",
      "estimated_fare": "50.00",
      "max_duration_hours": 24,
      "max_cycles": 48,
      "cancellation_charges": "5.00",
      "billing_note": "Charged per 30-minute cycle. Company pays directly."
    },
    "pickup_zone": {
      "id": 1,
      "name": "Downtown",
      "status": 1
    },
    "service": {
      "id": 1,
      "name": "Car",
      "half_hour_fare": "25.00"
    }
  }
}</pre>
                            </div>

                            <p><span class="status-code status-404">404</span> No Drivers Available</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "not_found",
  "status": "error",
  "message": {
    "error": ["No drivers available in this area"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Ride -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/create</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Create a new ride request. BCG Corporate system automatically assigns the best available
                            driver.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">service_id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Service type ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_location</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Pickup address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">pickup_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Pickup longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">destination_longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="optional">Optional</td>
                                        <td>Destination longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">ride_option</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>"go_now" or "schedule"</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">scheduled_time</td>
                                        <td class="param-type">datetime</td>
                                        <td class="optional">Required if scheduled</td>
                                        <td>Scheduled pickup time</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">estimated_duration</td>
                                        <td class="param-type">integer</td>
                                        <td class="optional">Optional</td>
                                        <td>Estimated duration in minutes</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">case_code</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>BCG case code for billing</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">stops</td>
                                        <td class="param-type">array</td>
                                        <td class="optional">Optional</td>
                                        <td>Multiple stops data</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">note</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Special instructions</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">number_of_passenger</td>
                                        <td class="param-type">integer</td>
                                        <td class="optional">Optional</td>
                                        <td>Number of passengers (default: 1)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_created_success",
  "status": "success",
  "message": {
    "success": ["Ride created successfully"]
  },
  "data": {
    "ride": {
      "id": 123,
      "uid": "RIDE123456",
      "pickup_location": "123 Main St",
      "destination": "456 Oak Ave",
      "ride_option": "go_now",
      "estimated_duration": 30,
      "cycles_count": 1,
      "fare_per_cycle": "25.00",
      "amount": "25.00",
      "case_code": "BCG2024001",
      "status": 0,
      "driver": {
        "id": 1,
        "name": "John Driver",
        "mobile": "1234567890",
        "vehicle": "Toyota Camry - ABC123"
      },
      "created_at": "2024-01-01T10:00:00.000000Z"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Authorization Section -->
        <div class="section" id="authorization">
            <h2 class="section-header">✅ Authorization & Verification</h2>
            <div class="section-content">

                <!-- Get Authorization Status -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/authorization</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Check user's verification status and get verification requirements.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "code_sent",
  "status": "success",
  "message": {
    "success": ["Verify your account"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resend Verification Code -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/resend-verify/{type}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Resend verification code for email or mobile verification.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">type</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Verification type: "email" or "sms"</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "code_sent",
  "status": "success",
  "message": {
    "success": ["Verification code sent successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Verify Email -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/verify-email</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Verify user's email address with verification code.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">email_verified_code</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>6-digit verification code</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "email_verified",
  "status": "success",
  "message": {
    "success": ["Email verified successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Verify Mobile -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/verify-mobile</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Verify user's mobile number with SMS verification code.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">sms_verified_code</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>6-digit SMS verification code</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "mobile_verified",
  "status": "success",
  "message": {
    "success": ["Mobile verified successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Ride List -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/ride/list</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get user's ride history with filtering options.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_list",
  "status": "success",
  "message": {
    "success": ["Ride list retrieved"]
  },
  "data": {
    "rides": [
      {
        "id": 123,
        "uid": "RIDE123456",
        "pickup_location": "123 Main St",
        "destination": "456 Oak Ave",
        "status": 3,
        "amount": "25.00",
        "created_at": "2024-01-01T10:00:00.000000Z",
        "driver": {
          "id": 1,
          "firstname": "John",
          "lastname": "Driver"
        }
      }
    ]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Ride Details -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/ride/details/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get detailed information about a specific ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_details",
  "status": "success",
  "message": {
    "success": ["Ride details retrieved"]
  },
  "data": {
    "ride": {
      "id": 123,
      "uid": "RIDE123456",
      "pickup_location": "123 Main St",
      "destination": "456 Oak Ave",
      "ride_option": "go_now",
      "estimated_duration": 30,
      "cycles_count": 1,
      "fare_per_cycle": "25.00",
      "amount": "25.00",
      "case_code": "BCG2024001",
      "status": 3,
      "started_at": "2024-01-01T10:30:00.000000Z",
      "ended_at": "2024-01-01T11:00:00.000000Z",
      "driver": {
        "id": 1,
        "firstname": "John",
        "lastname": "Driver",
        "mobile": "1234567890",
        "vehicle": {
          "model": "Toyota Camry",
          "color": "White",
          "vehicle_number": "ABC123"
        }
      }
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cancel Ride -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/cancel/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Cancel a pending or active ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">cancel_reason</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Reason for cancellation</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_canceled",
  "status": "success",
  "message": {
    "success": ["Ride canceled successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SOS Emergency -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/sos/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Send emergency SOS alert during an active ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Current latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Current longitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">message</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Emergency message</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "sos_sent",
  "status": "success",
  "message": {
    "success": ["SOS alert sent successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Management Section -->
        <div class="section" id="profile">
            <h2 class="section-header">👤 Profile Management</h2>
            <div class="section-content">

                <!-- Get User Info -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/user-info</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get current user information including BCG employee details.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "user_info",
  "status": "success",
  "message": {
    "success": ["User information retrieved"]
  },
  "data": {
    "user": {
      "id": 1,
      "firstname": "John",
      "lastname": "Doe",
      "email": "<EMAIL>",
      "employ_type": "BCG_EMPLOYEE",
      "full_name": "John Doe",
      "employ_id": "BCG12345",
      "phone": "+1234567890",
      "username": "johndoe",
      "mobile": "1234567890",
      "country_code": "US",
      "profile_complete": 1,
      "ev": 1,
      "sv": 1,
      "image_src": "https://example.com/image.jpg"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update BCG Profile -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/bcg-profile-update</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Update BCG employee profile information.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">full_name</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Full name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">employ_id</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Employee ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">phone</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Phone number</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">image</td>
                                        <td class="param-type">file</td>
                                        <td class="optional">Optional</td>
                                        <td>Profile image</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "profile_updated",
  "status": "success",
  "message": {
    "success": ["Profile updated successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Coupons Section -->
        <div class="section" id="coupons">
            <h2 class="section-header">🎫 Coupons</h2>
            <div class="section-content">

                <!-- Get Coupons -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/coupons</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get list of available coupons for the user.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "coupon",
  "status": "success",
  "message": {
    "success": ["Coupon code"]
  },
  "data": [
    {
      "id": 1,
      "name": "New User Discount",
      "code": "NEWUSER20",
      "discount_type": 1,
      "amount": "20.00",
      "minimum_amount": "50.00",
      "start_from": "2024-01-01",
      "end_at": "2024-12-31",
      "description": "20% discount for new users"
    }
  ]
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Apply Coupon -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/apply-coupon/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Apply a coupon to a ride for discount.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">coupon_code</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Coupon code to apply</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "coupon_applied",
  "status": "success",
  "message": {
    "success": ["Coupon applied successfully"]
  },
  "data": {
    "discount_amount": "10.00",
    "final_amount": "40.00"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Remove Coupon -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/remove-coupon/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Remove applied coupon from a ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "coupon_removed",
  "status": "success",
  "message": {
    "success": ["Coupon removed successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reviews Section -->
        <div class="section" id="reviews">
            <h2 class="section-header">⭐ Reviews</h2>
            <div class="section-content">

                <!-- Get User Reviews -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/review</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get user's review history and ratings given to drivers.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "review",
  "status": "success",
  "message": {
    "success": ["Rider review list"]
  },
  "data": {
    "reviews": [
      {
        "id": 1,
        "rating": 5,
        "review": "Excellent driver!",
        "created_at": "2024-01-01T10:00:00.000000Z",
        "ride": {
          "id": 123,
          "uid": "RIDE123456",
          "driver": {
            "id": 1,
            "firstname": "John",
            "lastname": "Driver"
          }
        }
      }
    ],
    "rider": {
      "id": 1,
      "firstname": "Jane",
      "lastname": "User"
    },
    "user_image_path": "path/to/user/images",
    "driver_image_path": "path/to/driver/images"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Review -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/review/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Submit a review and rating for a completed ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">review</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Review text</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">rating</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Rating (1-5 stars)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "success",
  "status": "success",
  "message": {
    "success": ["Review placed successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Get Driver Reviews -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/get-driver-review/{driverId}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get reviews and ratings for a specific driver.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">driverId</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Driver ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "review",
  "status": "success",
  "message": {
    "success": ["Driver review list"]
  },
  "data": {
    "reviews": [
      {
        "id": 1,
        "rating": 5,
        "review": "Great service!",
        "created_at": "2024-01-01T10:00:00.000000Z",
        "ride": {
          "id": 123,
          "uid": "RIDE123456",
          "user": {
            "id": 1,
            "firstname": "Jane",
            "lastname": "User"
          }
        }
      }
    ],
    "driver": {
      "id": 1,
      "firstname": "John",
      "lastname": "Driver",
      "avg_rating": "4.85",
      "total_reviews": 45,
      "vehicle": {
        "model": "Toyota Camry",
        "color": "White",
        "year": "2022"
      }
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Support Tickets Section -->
        <div class="section" id="support">
            <h2 class="section-header">🎧 Support Tickets</h2>
            <div class="section-content">

                <!-- Get Support Tickets -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/ticket/</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get list of user's support tickets.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "support_tickets",
  "status": "success",
  "message": {
    "success": ["Support tickets retrieved"]
  },
  "data": {
    "tickets": [
      {
        "id": 1,
        "ticket": "TKT-123456",
        "subject": "Payment Issue",
        "status": 1,
        "priority": 2,
        "last_reply": "2024-01-01T10:00:00.000000Z",
        "created_at": "2024-01-01T09:00:00.000000Z"
      }
    ]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Create Support Ticket -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ticket/create</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Create a new support ticket.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">subject</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Ticket subject</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">priority</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Priority level (1=Low, 2=Medium, 3=High)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">message</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Ticket message/description</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">attachments</td>
                                        <td class="param-type">array</td>
                                        <td class="optional">Optional</td>
                                        <td>File attachments</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ticket_created",
  "status": "success",
  "message": {
    "success": ["Support ticket created successfully"]
  },
  "data": {
    "ticket": {
      "id": 1,
      "ticket": "TKT-123456",
      "subject": "Payment Issue",
      "status": 1,
      "priority": 2
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Ticket -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/ticket/view/{ticket}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            View ticket details and conversation history.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">ticket</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Ticket number (e.g., TKT-123456)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ticket_details",
  "status": "success",
  "message": {
    "success": ["Ticket details retrieved"]
  },
  "data": {
    "ticket": {
      "id": 1,
      "ticket": "TKT-123456",
      "subject": "Payment Issue",
      "status": 1,
      "priority": 2,
      "messages": [
        {
          "id": 1,
          "message": "I have an issue with payment",
          "admin_id": null,
          "created_at": "2024-01-01T09:00:00.000000Z"
        }
      ]
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reply to Ticket -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ticket/reply/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Reply to an existing support ticket.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">message</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Reply message</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">attachments</td>
                                        <td class="param-type">array</td>
                                        <td class="optional">Optional</td>
                                        <td>File attachments</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ticket_replied",
  "status": "success",
  "message": {
    "success": ["Reply sent successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Messages Section -->
        <div class="section" id="messages">
            <h2 class="section-header">💬 Ride Messages</h2>
            <div class="section-content">

                <!-- Get Ride Messages -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/ride/messages/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get chat messages for a specific ride between user and driver.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_message",
  "status": "success",
  "message": {
    "success": ["Ride Messages"]
  },
  "data": {
    "messages": [
      {
        "id": 1,
        "ride_id": 123,
        "user_id": 1,
        "driver_id": null,
        "message": "I'm waiting at the pickup location",
        "image": null,
        "created_at": "2024-01-01T10:00:00.000000Z"
      },
      {
        "id": 2,
        "ride_id": 123,
        "user_id": null,
        "driver_id": 1,
        "message": "I'll be there in 2 minutes",
        "image": null,
        "created_at": "2024-01-01T10:01:00.000000Z"
      }
    ],
    "image_path": "path/to/message/images"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Send Message -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/ride/send/message/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Send a message to the driver during a ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">message</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Message text</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">image</td>
                                        <td class="param-type">file</td>
                                        <td class="optional">Optional</td>
                                        <td>Image attachment (jpg, jpeg, png)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "message",
  "status": "success",
  "message": {
    "success": ["Message send successfully"]
  },
  "data": {
    "message": {
      "id": 3,
      "ride_id": 123,
      "user_id": 1,
      "driver_id": null,
      "message": "Thank you for the update",
      "image": null,
      "created_at": "2024-01-01T10:02:00.000000Z"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="note">
            <h5>📝 Important Notes</h5>
            <ul>
                <li><strong>Authentication:</strong> All protected endpoints require Bearer token in Authorization
                    header</li>
                <li><strong>BCG Corporate:</strong> Special features available for BCG employees including direct
                    billing and enhanced ride options</li>
                <li><strong>Rate Limiting:</strong> API calls are rate limited to prevent abuse</li>
                <li><strong>Error Handling:</strong> All errors follow consistent format with remark, status, and
                    message fields</li>
                <li><strong>Timestamps:</strong> All timestamps are in UTC format</li>
            </ul>
        </div>

        <div class="section">
            <h2 class="section-header">📞 Support & Contact</h2>
            <div class="section-content">
                <p>For technical support or API questions, please contact:</p>
                <ul>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Documentation:</strong> <a href="#" target="_blank">Developer Portal</a></li>
                    <li><strong>Status Page:</strong> <a href="#" target="_blank">API Status</a></li>
                </ul>
            </div>
        </div>
    </div>
</body>

</html>