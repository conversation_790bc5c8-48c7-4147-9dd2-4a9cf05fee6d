<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>User API Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Arial, sans-serif;
            margin: 2em;
            background: linear-gradient(120deg, #f9f9f9 60%, #e3e9f7 100%);
        }

        h1 {
            font-size: 2.5em;
            margin-bottom: 0.5em;
            letter-spacing: 1px;
        }

        h2 {
            margin-top: 0;
            font-size: 1.5em;
        }

        h3 {
            margin-bottom: 0.3em;
            font-size: 1.1em;
        }

        .endpoint {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 24px #e3e9f7;
            margin-bottom: 2.5em;
            padding: 2em 2em 1.5em 2em;
            border-left: 6px solid #3498db;
            transition: box-shadow 0.2s;
        }

        .endpoint:hover {
            box-shadow: 0 8px 32px #b3c6e7;
        }

        .method {
            font-weight: bold;
            color: #fff;
            padding: 2px 12px;
            border-radius: 4px;
            font-size: 1em;
            margin-right: 8px;
        }

        .get {
            background: #3498db;
        }

        .post {
            background: #27ae60;
        }

        .any {
            background: #8e44ad;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 1em 0 0.5em 0;
            font-size: 1em;
        }

        .param-table th,
        .param-table td {
            border: 1px solid #e3e9f7;
            padding: 10px 8px;
        }

        .param-table th {
            background: #f4f8fb;
            color: #2c3e50;
            font-weight: 600;
        }

        .param-table tr:nth-child(even) td {
            background: #f9fbfd;
        }

        .response {
            background: #f4f8fb;
            border-left: 4px solid #3498db;
            padding: 1em;
            margin-top: 1em;
            font-family: 'Fira Mono', 'Consolas', monospace;
            font-size: 1em;
            color: #222;
            overflow-x: auto;
        }

        @media (max-width: 700px) {
            body {
                margin: 0.5em;
            }

            .endpoint {
                padding: 1em 0.5em;
            }

            .param-table th,
            .param-table td {
                padding: 6px 4px;
                font-size: 0.95em;
            }
        }
    </style>
</head>

<body>
    <h1>User API Documentation</h1>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/register</h2>
        <h3>Description</h3>
        <p>Register a new user account. Only minimal required fields are accepted.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>email</td>
                <td>string</td>
                <td>Yes</td>
                <td>User email (must be @bcg.com)</td>
            </tr>
            <tr>
                <td>employ_type</td>
                <td>string</td>
                <td>Yes</td>
                <td>premium or premium_plus</td>
            </tr>
            <tr>
                <td>password</td>
                <td>string</td>
                <td>Yes</td>
                <td>Password (min 6 chars, confirmed)</td>
            </tr>
            <tr>
                <td>password_confirmation</td>
                <td>string</td>
                <td>Yes</td>
                <td>Must match password</td>
            </tr>
        </table>
        <h3>Example Request</h3>
        <div class="response">{
    "email": "<EMAIL>",
    "employ_type": "premium",
    "password": "Password123!",
    "password_confirmation": "Password123!",
}</div>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "registration_success",
    "status": "success",
    "message": ["Registration successful"],
    "data": {
        "access_token": "...",
        "user": { ... },
        "token_type": "Bearer"
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/login</h2>
        <h3>Description</h3>
        <p>Login with email and password.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>email</td>
                <td>string</td>
                <td>Yes</td>
                <td>User email</td>
            </tr>
            <tr>
                <td>password</td>
                <td>string</td>
                <td>Yes</td>
                <td>Password</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "login_success",
            "status": "success",
            "message": ["Login successful"],
            "data": {
            "user": {
            "id": 1,
            "email": "<EMAIL>",
            "full_name": "John Doe"
            },
            "token": "...jwt or sanctum token..."
            }
            }</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/user/forgot-password</h2>
        <h3>Description</h3>
        <p>Request a password reset link.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>email</td>
                <td>string</td>
                <td>Yes</td>
                <td>User email</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "reset_link_sent",
            "status": "success",
            "message": ["Password reset link sent to your email"]
            }</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/user/reset-password</h2>
        <h3>Description</h3>
        <p>Reset password using token.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>email</td>
                <td>string</td>
                <td>Yes</td>
                <td>User email</td>
            </tr>
            <tr>
                <td>token</td>
                <td>string</td>
                <td>Yes</td>
                <td>Reset token</td>
            </tr>
            <tr>
                <td>password</td>
                <td>string</td>
                <td>Yes</td>
                <td>New password</td>
            </tr>
            <tr>
                <td>password_confirmation</td>
                <td>string</td>
                <td>Yes</td>
                <td>Must match password</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "password_reset",
            "status": "success",
            "message": ["Password has been reset successfully"]
            }</div>
    </div>

    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/user-info</h2>
        <h3>Description</h3>
        <p>Get authenticated user info. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "user_info",
            "status": "success",
            "message": ["User info fetched"],
            "data": {
            "user": {
            "id": 1,
            "email": "<EMAIL>",
            "full_name": "John Doe",
            ...
            }
            }
            }</div>
    </div>

    <!-- Coupon APIs -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/coupons</h2>
        <h3>Description</h3>
        <p>Get available coupons. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "coupons",
            "status": "success",
            "message": ["Coupons fetched"],
            "data": {
            "coupons": [ ... ]
            }
            }</div>
    </div>

    <!-- Review APIs -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/review</h2>
        <h3>Description</h3>
        <p>Get user reviews. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "review",
            "status": "success",
            "message": ["User Review List"],
            "data": {
            "reviews": [ ... ]
            }
            }</div>
    </div>

    <!-- Ticket APIs -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/ticket</h2>
        <h3>Description</h3>
        <p>Get support tickets. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "support_ticket",
            "status": "success",
            "message": ["Support tickets fetched"],
            "data": {
            "tickets": [ ... ]
            }
            }</div>
    </div>

    <!-- Ride APIs (Expanded) -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/ride/list</h2>
        <h3>Description</h3>
        <p>Get list of rides. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "ride_list",
            "status": "success",
            "message": ["Ride list fetched"],
            "data": {
            "rides": [ ... ]
            }
            }</div>
    </div>

    <!-- Profile Update -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/profile-setting</h2>
        <h3>Description</h3>
        <p>Complete or update user profile. Only minimal required fields are accepted.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>dial_code</td>
                <td>string</td>
                <td>Yes</td>
                <td>Mobile dial code (e.g. +1, +91)</td>
            </tr>
            <tr>
                <td>phone</td>
                <td>string</td>
                <td>Yes</td>
                <td>Mobile number (unique per dial code)</td>
            </tr>
            <tr>
                <td>employ_id</td>
                <td>string</td>
                <td>No</td>
                <td>Employee ID</td>
            </tr>
            <tr>
                <td>full_name</td>
                <td>string</td>
                <td>No</td>
                <td>Full name</td>
            </tr>
        </table>
        <h3>Example Request</h3>
        <div class="response">{
    "dial_code": "+1",
    "phone": "1234567890",
    "employ_id": "EMP123",
    "full_name": "John Doe"
}</div>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "profile_completed",
    "status": "success",
    "message": ["Profile completed successfully"],
    "data": {
        "user": { ... }
    }
}</div>
    </div>

    <!-- Change Password -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/change-password</h2>
        <h3>Description</h3>
        <p>Change user password. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>current_password</td>
                <td>string</td>
                <td>Yes</td>
                <td>Current password</td>
            </tr>
            <tr>
                <td>password</td>
                <td>string</td>
                <td>Yes</td>
                <td>New password</td>
            </tr>
            <tr>
                <td>password_confirmation</td>
                <td>string</td>
                <td>Yes</td>
                <td>Must match password</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "password_changed",
            "status": "success",
            "message": ["Password changed successfully"]
            }</div>
    </div>

    <!-- Save Device Token -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/save-device-token</h2>
        <h3>Description</h3>
        <p>Save device token for push notifications. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>token</td>
                <td>string</td>
                <td>Yes</td>
                <td>Device token</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "token_saved",
            "status": "success",
            "message": ["Token saved successfully"]
            }</div>
    </div>

    <!-- Push Notifications Read -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/push-notifications/read/{id}</h2>
        <h3>Description</h3>
        <p>Mark a push notification as read. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>id</td>
                <td>integer</td>
                <td>Yes (URL param)</td>
                <td>Notification ID</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "notification_read",
            "status": "success",
            "message": ["Notification marked as read"]
            }</div>
    </div>

    <!-- Delete Account -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/delete-account</h2>
        <h3>Description</h3>
        <p>Delete user account. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
            "remark": "account_delete",
            "status": "success",
            "message": ["Account deleted successfully"]
            }</div>
    </div>

    <!-- Pusher Auth -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/pusher/auth/{socketId}/{channelName}</h2>
        <h3>Description</h3>
        <p>Pusher authentication for private channels. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr>
                <th>Name</th>
                <th>Type</th>
                <th>Required</th>
                <th>Description</th>
            </tr>
            <tr>
                <td>socketId</td>
                <td>string</td>
                <td>Yes (URL param)</td>
                <td>Pusher socket ID</td>
            </tr>
            <tr>
                <td>channelName</td>
                <td>string</td>
                <td>Yes (URL param)</td>
                <td>Pusher channel name</td>
            </tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
            "auth": "...pusher auth string..."
            }</div>
    </div>

    <!-- Add more endpoints as needed -->


    <!-- Ride Receipt -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/ride/receipt/{id}</h2>
        <h3>Description</h3>
        <p>Get ride receipt. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "ride_receipt",
    "status": "success",
    "message": ["Ride receipt fetched"],
    "data": {
        "receipt": { ... }
    }
}</div>
    </div>

    <!-- Ride APIs (Expanded) -->
    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/available-drivers</h2>
        <h3>Description</h3>
        <p>Get available drivers and fare estimate for a ride. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>service_id</td><td>integer</td><td>Yes</td><td>Service type ID</td></tr>
            <tr><td>pickup_latitude</td><td>float</td><td>Yes</td><td>Pickup latitude</td></tr>
            <tr><td>pickup_longitude</td><td>float</td><td>Yes</td><td>Pickup longitude</td></tr>
            <tr><td>destination_latitude</td><td>float</td><td>No</td><td>Destination latitude</td></tr>
            <tr><td>destination_longitude</td><td>float</td><td>No</td><td>Destination longitude</td></tr>
            <tr><td>ride_option</td><td>string</td><td>Yes</td><td>go_now or schedule</td></tr>
            <tr><td>scheduled_time</td><td>string</td><td>Required if ride_option is schedule</td><td>Scheduled time (Y-m-d H:i:s)</td></tr>
            <tr><td>stops</td><td>array</td><td>No</td><td>Array of stops (each with latitude, longitude, address, note)</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "driver_available",
    "status": "success",
    "message": ["Driver will be automatically assigned by system"],
    "data": {
        "service_available": true,
        "fare_data": {
            "estimated_duration_minutes": 30,
            "cycles_count": 1,
            "fare_per_cycle": 100,
            "estimated_fare": 100,
            "max_duration_hours": 24,
            "max_cycles": 48,
            "cancellation_charges": 50,
            "billing_note": "Charged per 30-minute cycle. Company pays directly."
        },
        "ride_option": "go_now",
        "scheduled_time": null,
        "pickup_zone": { ... },
        "service_types": [ ... ]
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/create</h2>
        <h3>Description</h3>
        <p>Create a new ride request. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>service_id</td><td>integer</td><td>Yes</td><td>Service type ID</td></tr>
            <tr><td>pickup_latitude</td><td>float</td><td>Yes</td><td>Pickup latitude</td></tr>
            <tr><td>pickup_longitude</td><td>float</td><td>Yes</td><td>Pickup longitude</td></tr>
            <tr><td>destination_latitude</td><td>float</td><td>No</td><td>Destination latitude</td></tr>
            <tr><td>destination_longitude</td><td>float</td><td>No</td><td>Destination longitude</td></tr>
            <tr><td>note</td><td>string</td><td>No</td><td>Additional notes</td></tr>
            <tr><td>number_of_passenger</td><td>integer</td><td>Yes</td><td>Number of passengers (1-4)</td></tr>
            <tr><td>ride_option</td><td>string</td><td>Yes</td><td>go_now or schedule</td></tr>
            <tr><td>scheduled_time</td><td>string</td><td>Required if ride_option is schedule</td><td>Scheduled time (Y-m-d H:i:s)</td></tr>
            <tr><td>estimated_duration</td><td>integer</td><td>No</td><td>Estimated duration in minutes (30-1440)</td></tr>
            <tr><td>case_code</td><td>string</td><td>Yes</td><td>BCG Case Code</td></tr>
            <tr><td>stops</td><td>array</td><td>No</td><td>Array of stops (each with latitude, longitude, address, note)</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "bcg_ride_created",
    "status": "success",
    "message": ["Ride created and driver assigned successfully"],
    "data": {
        "ride": { ... },
        "driver": { ... },
        "fare_breakdown": {
            "cycles": 1,
            "fare_per_cycle": 100,
            "estimated_fare": 100,
            "billing_note": "Charged per 30-minute cycle. Company pays directly."
        }
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/ride/details/{id}</h2>
        <h3>Description</h3>
        <p>Get details of a specific ride. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "ride_details",
    "status": "success",
    "message": ["Ride Details"],
    "data": {
        "ride": { ... },
        "service_image_path": "...",
        "brand_image_path": "...",
        "user_image_path": "...",
        "driver_image_path": "...",
        "driver_total_ride": 10
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/cancel/{id}</h2>
        <h3>Description</h3>
        <p>Cancel a ride request. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
            <tr><td>cancel_reason</td><td>string</td><td>Yes</td><td>Reason for cancellation</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "canceled_ride",
    "status": "success",
    "message": ["Ride canceled successfully"]
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/sos/{id}</h2>
        <h3>Description</h3>
        <p>Send SOS alert for a running ride. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
            <tr><td>latitude</td><td>float</td><td>Yes</td><td>Latitude of SOS</td></tr>
            <tr><td>longitude</td><td>float</td><td>Yes</td><td>Longitude of SOS</td></tr>
            <tr><td>message</td><td>string</td><td>No</td><td>Optional message</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "sos_request",
    "status": "success",
    "message": ["SOS request successfully"]
}</div>
    </div>


    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/payment/{id}</h2>
        <h3>Description</h3>
        <p>Save payment for a ride. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
            <tr><td>payment_type</td><td>string</td><td>Yes</td><td>gateway or cash</td></tr>
            <tr><td>method_code</td><td>string</td><td>Required if payment_type is gateway</td><td>Payment gateway code</td></tr>
            <tr><td>currency</td><td>string</td><td>Required if payment_type is gateway</td><td>Currency code</td></tr>
            <tr><td>tips_amount</td><td>float</td><td>Yes</td><td>Tips amount</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "cash_payment",
    "status": "success",
    "message": ["Please give the driver 100 in cash."],
    "data": {
        "ride": { ... }
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/ride/receipt/{id}</h2>
        <h3>Description</h3>
        <p>Get ride receipt (PDF). Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">PDF stream</div>
    </div>

    <!-- Ride Messaging -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/ride/messages/{id}</h2>
        <h3>Description</h3>
        <p>Get messages for a ride. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "ride_message",
    "status": "success",
    "message": ["Ride Messages"],
    "data": {
        "messages": [ ... ],
        "image_path": "..."
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/message/{id}</h2>
        <h3>Description</h3>
        <p>Send a message in a ride chat. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
            <tr><td>message</td><td>string</td><td>Yes</td><td>Message text</td></tr>
            <tr><td>image</td><td>file</td><td>No</td><td>Optional image (jpg, jpeg, png)</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "ride_message_sent",
    "status": "success",
    "message": ["Message sent successfully"],
    "data": {
        "message": { ... },
        "ride": { ... }
    }
}</div>
    </div>


    <!-- Review APIs (Expanded) -->
    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/review</h2>
        <h3>Description</h3>
        <p>Get user reviews. Requires Authorization header.</p>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "review",
    "status": "success",
    "message": ["Rider review list"],
    "data": {
        "user_image_path": "...",
        "driver_image_path": "...",
        "reviews": [ ... ],
        "rider": { ... }
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method get">GET</span> /api/driver/review/{driverId}</h2>
        <h3>Description</h3>
        <p>Get reviews for a specific driver. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>driverId</td><td>integer</td><td>Yes (URL param)</td><td>Driver ID</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "review",
    "status": "success",
    "message": ["Driver review list"],
    "data": {
        "user_image_path": "...",
        "driver_image_path": "...",
        "reviews": [ ... ],
        "driver": { ... }
    }
}</div>
    </div>

    <div class="endpoint">
        <h2><span class="method post">POST</span> /api/ride/review/{id}</h2>
        <h3>Description</h3>
        <p>Submit a review and rating for a completed ride. Requires Authorization header.</p>
        <h3>Parameters</h3>
        <table class="param-table">
            <tr><th>Name</th><th>Type</th><th>Required</th><th>Description</th></tr>
            <tr><td>id</td><td>integer</td><td>Yes (URL param)</td><td>Ride ID</td></tr>
            <tr><td>review</td><td>string</td><td>Yes</td><td>Review text</td></tr>
            <tr><td>rating</td><td>integer</td><td>Yes</td><td>Rating (1-5)</td></tr>
        </table>
        <h3>Example Response</h3>
        <div class="response">{
    "remark": "success",
    "status": "success",
    "message": ["Review placed successfully"]
}</div>
    </div>

</body>

</html>