<?php $__env->startSection('panel'); ?>
    <?php
        $smsConfig = gs('sms_config');
    ?>
    <div class="row">
        <div class="col-md-12">
            <?php if (isset($component)) { $__componentOriginalfdb23fa6017278bcd751b09e9d04fdc0 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfdb23fa6017278bcd751b09e9d04fdc0 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.card.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.card'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                <?php if (isset($component)) { $__componentOriginal82a520cb144a92d0fb68c226771dfec2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal82a520cb144a92d0fb68c226771dfec2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.card.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.card.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                    <form method="POST">
                        <?php echo csrf_field(); ?>
                        <div class="form-group">
                            <label><?php echo app('translator')->get('Sms Send Method'); ?></label>
                            <select name="sms_method" class="select2 form-control select2-100" data-minimum-results-for-search="-1">
                                <option value="clickatell" <?php if(@$smsConfig->name == 'clickatell'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Clickatell'); ?></option>
                                <option value="infobip" <?php if(@$smsConfig->name == 'infobip'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Infobip'); ?>
                                </option>
                                <option value="messageBird" <?php if(@$smsConfig->name == 'messageBird'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Message Bird'); ?></option>
                                <option value="nexmo" <?php if(@$smsConfig->name == 'nexmo'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Nexmo'); ?>
                                </option>
                                <option value="smsBroadcast" <?php if(@$smsConfig->name == 'smsBroadcast'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Sms Broadcast'); ?></option>
                                <option value="twilio" <?php if(@$smsConfig->name == 'twilio'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Twilio'); ?>
                                </option>
                                <option value="textMagic" <?php if(@$smsConfig->name == 'textMagic'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Text Magic'); ?></option>
                                <option value="custom" <?php if(@$smsConfig->name == 'custom'): ?> selected <?php endif; ?>>
                                    <?php echo app('translator')->get('Custom API'); ?>
                                </option>
                            </select>
                        </div>
                        <div class="row mt-4 d-none configForm" id="clickatell">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Clickatell Configuration'); ?></h6>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('API Key'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('API Key'); ?>"
                                        name="clickatell_api_key" value="<?php echo e(@$smsConfig->clickatell->api_key); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="infobip">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Infobip Configuration'); ?></h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Username'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Username'); ?>"
                                        name="infobip_username" value="<?php echo e(@$smsConfig->infobip->username); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Password'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Password'); ?>"
                                        name="infobip_password" value="<?php echo e(@$smsConfig->infobip->password); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="messageBird">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Message Bird Configuration'); ?></h6>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('API Key'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('API Key'); ?>"
                                        name="message_bird_api_key" value="<?php echo e(@$smsConfig->message_bird->api_key); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="nexmo">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Nexmo Configuration'); ?></h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('API Key'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('API Key'); ?>"
                                        name="nexmo_api_key" value="<?php echo e(@$smsConfig->nexmo->api_key); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('API Secret'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('API Secret'); ?>"
                                        name="nexmo_api_secret" value="<?php echo e(@$smsConfig->nexmo->api_secret); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="smsBroadcast">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Sms Broadcast Configuration'); ?></h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Username'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Username'); ?>"
                                        name="sms_broadcast_username" value="<?php echo e(@$smsConfig->sms_broadcast->username); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Password'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Password'); ?>"
                                        name="sms_broadcast_password" value="<?php echo e(@$smsConfig->sms_broadcast->password); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="twilio">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Twilio Configuration'); ?></h6>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Account SID'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Account SID'); ?>"
                                        name="account_sid" value="<?php echo e(@$smsConfig->twilio->account_sid); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Auth Token'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Auth Token'); ?>"
                                        name="auth_token" value="<?php echo e(@$smsConfig->twilio->auth_token); ?>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('From Number'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('From Number'); ?>"
                                        name="from" value="<?php echo e(@$smsConfig->twilio->from); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="textMagic">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Text Magic Configuration'); ?></h6>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Username'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Username'); ?>"
                                        name="text_magic_username" value="<?php echo e(@$smsConfig->text_magic->username); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('Apiv2 Key'); ?> </label>
                                    <input type="text" class="form-control" placeholder="<?php echo app('translator')->get('Apiv2 Key'); ?>"
                                        name="apiv2_key" value="<?php echo e(@$smsConfig->text_magic->apiv2_key); ?>">
                                </div>
                            </div>
                        </div>
                        <div class="row mt-4 d-none configForm" id="custom">
                            <div class="col-md-12">
                                <h6 class="mb-2"><?php echo app('translator')->get('Custom API'); ?></h6>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label><?php echo app('translator')->get('API URL'); ?> </label>
                                    <div class="input-group">
                                        <span class="input-group-text">
                                            <select name="custom_api_method" class="method-select">
                                                <option value="get"><?php echo app('translator')->get('GET'); ?></option>
                                                <option value="post"><?php echo app('translator')->get('POST'); ?></option>
                                            </select>
                                        </span>
                                        <input type="text" class="form-control" name="custom_api_url"
                                            value="<?php echo e(@$smsConfig->custom->url); ?>" placeholder="<?php echo app('translator')->get('API URL'); ?>">
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="row">
                                <div class="col-md-12">
                                    <div class=" table-responsive table-responsive--sm mb-3">
                                        <table class="  table align-items-center table--light">
                                            <thead>
                                                <tr>
                                                    <th><?php echo app('translator')->get('Short Code'); ?> </th>
                                                    <th><?php echo app('translator')->get('Description'); ?></th>
                                                </tr>
                                            </thead>
                                            
                                            <tbody class="list">
                                                <tr>
                                                    <td><span class="copyBtn cursor-pointer" data-copy="{{message}}">{{message}}</span></td>
                                                    <td><?php echo app('translator')->get('Message'); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><span class="copyBtn cursor-pointer" data-copy="{{number}}">{{number}}</span></td>
                                                    <td><?php echo app('translator')->get('Number'); ?></td>
                                                </tr>
                                            </tbody>
                                            
                                        </table>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border--gray mb-3 ">
                                        <div
                                            class="card-header bg--gray d-flex justify-content-between align-items-center">
                                            <h5 class="card-title mb-0"><?php echo app('translator')->get('Headers'); ?></h5>
                                            <button type="button" class="btn   addHeader btn--primary">
                                                <i class="la la-fw la-plus"></i>
                                                <?php echo app('translator')->get('Add'); ?>
                                            </button>
                                        </div>
                                        <div class="card-body">
                                            <div class="headerFields">
                                                <?php for($i = 0; $i < count(gs('sms_config')->custom->headers->name); $i++): ?>
                                                    <div class="row mt-3">
                                                        <div class="col-md-5">
                                                            <input type="text" name="custom_header_name[]"
                                                                class="form-control"
                                                                value="<?php echo e(@$smsConfig->custom->headers->name[$i]); ?>"
                                                                placeholder="<?php echo app('translator')->get('Headers Name'); ?>">
                                                        </div>
                                                        <div class="col-md-5">
                                                            <input type="text" name="custom_header_value[]"
                                                                class="form-control"
                                                                value="<?php echo e(@$smsConfig->custom->headers->value[$i]); ?>"
                                                                placeholder="<?php echo app('translator')->get('Headers Value'); ?>">
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button"
                                                                class="w-100 btn btn--danger btn-block removeHeader h-100"><i
                                                                    class="las la-times"></i></button>
                                                        </div>
                                                    </div>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border--gray mb-3 ">
                                        <div
                                            class="card-header bg--gray d-flex justify-content-between align-items-center">
                                            <h5 class="card-title"><?php echo app('translator')->get('Body'); ?></h5>
                                            <button type="button"
                                                class="btn  border-white  addHeader border-white addBody">
                                                <i class="la la-fw la-plus"></i><?php echo app('translator')->get('Add'); ?>
                                            </button>
                                        </div>
                                        <div class="card-body">
                                            <div class="bodyFields">
                                                <?php for($i = 0; $i < count(gs('sms_config')->custom->body->name); $i++): ?>
                                                    <div class="row mt-3">
                                                        <div class="col-md-5">
                                                            <input type="text" name="custom_body_name[]"
                                                                class="form-control"
                                                                value="<?php echo e(@$smsConfig->custom->body->name[$i]); ?>"
                                                                placeholder="<?php echo app('translator')->get('Body Name'); ?>">
                                                        </div>
                                                        <div class="col-md-5">
                                                            <input type="text" name="custom_body_value[]"
                                                                value="<?php echo e(@$smsConfig->custom->body->value[$i]); ?>"
                                                                class="form-control" placeholder="<?php echo app('translator')->get('Body Value'); ?>">
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button"
                                                                class="w-100 btn btn--danger btn-block removeBody h-100"><i
                                                                    class="las la-times"></i></button>
                                                        </div>
                                                    </div>
                                                <?php endfor; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php if (isset($component)) { $__componentOriginalf0d8a33ca9a77ded232a88985df19e6a = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf0d8a33ca9a77ded232a88985df19e6a = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.btn.submit','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.btn.submit'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf0d8a33ca9a77ded232a88985df19e6a)): ?>
<?php $attributes = $__attributesOriginalf0d8a33ca9a77ded232a88985df19e6a; ?>
<?php unset($__attributesOriginalf0d8a33ca9a77ded232a88985df19e6a); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf0d8a33ca9a77ded232a88985df19e6a)): ?>
<?php $component = $__componentOriginalf0d8a33ca9a77ded232a88985df19e6a; ?>
<?php unset($__componentOriginalf0d8a33ca9a77ded232a88985df19e6a); ?>
<?php endif; ?>
                    </form>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal82a520cb144a92d0fb68c226771dfec2)): ?>
<?php $attributes = $__attributesOriginal82a520cb144a92d0fb68c226771dfec2; ?>
<?php unset($__attributesOriginal82a520cb144a92d0fb68c226771dfec2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal82a520cb144a92d0fb68c226771dfec2)): ?>
<?php $component = $__componentOriginal82a520cb144a92d0fb68c226771dfec2; ?>
<?php unset($__componentOriginal82a520cb144a92d0fb68c226771dfec2); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfdb23fa6017278bcd751b09e9d04fdc0)): ?>
<?php $attributes = $__attributesOriginalfdb23fa6017278bcd751b09e9d04fdc0; ?>
<?php unset($__attributesOriginalfdb23fa6017278bcd751b09e9d04fdc0); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfdb23fa6017278bcd751b09e9d04fdc0)): ?>
<?php $component = $__componentOriginalfdb23fa6017278bcd751b09e9d04fdc0; ?>
<?php unset($__componentOriginalfdb23fa6017278bcd751b09e9d04fdc0); ?>
<?php endif; ?>
        </div>
    </div>

    <?php if (isset($component)) { $__componentOriginal9accb1dbf6cf6778783614568082f170 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9accb1dbf6cf6778783614568082f170 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.modal.index','data' => ['id' => 'testSMSModal']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'testSMSModal']); ?>
        <?php if (isset($component)) { $__componentOriginal4d0771d53f8ea302c36e9f849f65de02 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4d0771d53f8ea302c36e9f849f65de02 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.modal.header','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.modal.header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <h1 class="modal-title"><?php echo app('translator')->get('Test SMS Setup'); ?></h1>
            <button type="button" class="btn-close close" data-bs-dismiss="modal" aria-label="Close">
                <i class="las la-times"></i>
            </button>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4d0771d53f8ea302c36e9f849f65de02)): ?>
<?php $attributes = $__attributesOriginal4d0771d53f8ea302c36e9f849f65de02; ?>
<?php unset($__attributesOriginal4d0771d53f8ea302c36e9f849f65de02); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4d0771d53f8ea302c36e9f849f65de02)): ?>
<?php $component = $__componentOriginal4d0771d53f8ea302c36e9f849f65de02; ?>
<?php unset($__componentOriginal4d0771d53f8ea302c36e9f849f65de02); ?>
<?php endif; ?>
        <?php if (isset($component)) { $__componentOriginalade637066b100c66dd5d24d56c11248b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalade637066b100c66dd5d24d56c11248b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.modal.body','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.modal.body'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
            <form action="<?php echo e(route('admin.setting.notification.sms.test')); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="form-group">
                    <label><?php echo app('translator')->get('Sent to'); ?> </label>
                    <input type="text" name="mobile" class="form-control" placeholder="<?php echo app('translator')->get('Mobile'); ?>">
                </div>
                <input type="hidden" name="id">
                <div class="form-group">
                    <?php if (isset($component)) { $__componentOriginal907f3db99809b71a91ff0426ce516517 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal907f3db99809b71a91ff0426ce516517 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.ui.btn.modal','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.ui.btn.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal907f3db99809b71a91ff0426ce516517)): ?>
<?php $attributes = $__attributesOriginal907f3db99809b71a91ff0426ce516517; ?>
<?php unset($__attributesOriginal907f3db99809b71a91ff0426ce516517); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal907f3db99809b71a91ff0426ce516517)): ?>
<?php $component = $__componentOriginal907f3db99809b71a91ff0426ce516517; ?>
<?php unset($__componentOriginal907f3db99809b71a91ff0426ce516517); ?>
<?php endif; ?>
                </div>
            </form>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalade637066b100c66dd5d24d56c11248b)): ?>
<?php $attributes = $__attributesOriginalade637066b100c66dd5d24d56c11248b; ?>
<?php unset($__attributesOriginalade637066b100c66dd5d24d56c11248b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalade637066b100c66dd5d24d56c11248b)): ?>
<?php $component = $__componentOriginalade637066b100c66dd5d24d56c11248b; ?>
<?php unset($__componentOriginalade637066b100c66dd5d24d56c11248b); ?>
<?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9accb1dbf6cf6778783614568082f170)): ?>
<?php $attributes = $__attributesOriginal9accb1dbf6cf6778783614568082f170; ?>
<?php unset($__attributesOriginal9accb1dbf6cf6778783614568082f170); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9accb1dbf6cf6778783614568082f170)): ?>
<?php $component = $__componentOriginal9accb1dbf6cf6778783614568082f170; ?>
<?php unset($__componentOriginal9accb1dbf6cf6778783614568082f170); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('breadcrumb-plugins'); ?>
    <button type="button" data-bs-target="#testSMSModal" data-bs-toggle="modal" class="btn btn--primary "> <i
            class="fa-regular fa-paper-plane"></i> <?php echo app('translator')->get('Send Test SMS'); ?></button>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('style'); ?>
    <style>
        .method-select {
            padding: 2px 7px;
        }
    </style>
<?php $__env->stopPush(); ?>


<?php $__env->startPush('script'); ?>
    <script>
        (function($) {
            "use strict";



            var method = '<?php echo e(@$smsConfig->name); ?>';

            if (!method) {
                method = 'clickatell';
            }

            smsMethod(method);
            $('select[name=sms_method]').on('change', function() {
                var method = $(this).val();
                smsMethod(method);
            });

            function smsMethod(method) {
                $('.configForm').addClass('d-none');
                if (method != 'php') {
                    $(`#${method}`).removeClass('d-none');
                }
            }

            $('.addHeader').on('click', function() {
                var html = `
                    <div class="row mt-3">
                        <div class="col-md-5">
                            <input type="text" name="custom_header_name[]" class="form-control" placeholder="<?php echo app('translator')->get('Headers Name'); ?>">
                        </div>
                        <div class="col-md-5">
                            <input type="text" name="custom_header_value[]" class="form-control" placeholder="<?php echo app('translator')->get('Headers Value'); ?>">
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="w-100 btn btn--danger btn-block removeHeader h-100"><i class="las la-times"></i></button>
                        </div>
                    </div>
                `;
                $('.headerFields').append(html);

            })
            $(document).on('click', '.removeHeader', function() {
                $(this).closest('.row').remove();
            })

            $('.addBody').on('click', function() {
                var html = `
                    <div class="row mt-3">
                        <div class="col-md-5">
                            <input type="text" name="custom_body_name[]" class="form-control" placeholder="<?php echo app('translator')->get('Body Name'); ?>">
                        </div>
                        <div class="col-md-5">
                            <input type="text" name="custom_body_value[]" class="form-control" placeholder="<?php echo app('translator')->get('Body Value'); ?>">
                        </div>
                        <div class="col-md-2">
                            <button type="button" class="w-100 btn btn--danger btn-block removeBody h-100"><i class="las la-times"></i></button>
                        </div>
                    </div>
                `;
                $('.bodyFields').append(html);

            })
            $(document).on('click', '.removeBody', function() {
                $(this).closest('.row').remove();
            })

            $('select[name=custom_api_method]').val('<?php echo e(@$smsConfig->custom->method); ?>');

        })(jQuery);
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\swt_app\core\resources\views/admin/notification/sms_setting.blade.php ENDPATH**/ ?>