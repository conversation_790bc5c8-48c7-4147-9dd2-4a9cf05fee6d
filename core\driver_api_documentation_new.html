<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Driver API Documentation - SWT App</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }

        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }

        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .nav h3 {
            margin-top: 0;
            color: #333;
        }

        .nav ul {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .nav li {
            margin: 5px 0;
        }

        .nav a {
            color: #28a745;
            text-decoration: none;
            padding: 8px 12px;
            border-radius: 5px;
            display: block;
            transition: background-color 0.3s;
        }

        .nav a:hover {
            background-color: #e8f5e8;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .section-header {
            background: #28a745;
            color: white;
            padding: 20px;
            margin: 0;
        }

        .section-content {
            padding: 30px;
        }

        .endpoint {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 30px;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .method {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
            margin-right: 10px;
        }

        .method.get {
            background: #d4edda;
            color: #155724;
        }

        .method.post {
            background: #d1ecf1;
            color: #0c5460;
        }

        .method.put {
            background: #fff3cd;
            color: #856404;
        }

        .method.delete {
            background: #f8d7da;
            color: #721c24;
        }

        .endpoint-url {
            font-family: 'Courier New', monospace;
            font-size: 1.1em;
            color: #333;
        }

        .endpoint-content {
            padding: 20px;
        }

        .description {
            margin-bottom: 20px;
            color: #666;
        }

        .params-section,
        .response-section {
            margin-bottom: 25px;
        }

        .params-section h4,
        .response-section h4 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #28a745;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .param-table th,
        .param-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .param-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .param-table .param-name {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #28a745;
        }

        .param-table .param-type {
            color: #007bff;
            font-style: italic;
        }

        .param-table .required {
            color: #dc3545;
            font-weight: bold;
        }

        .param-table .optional {
            color: #6c757d;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .json {
            color: #333;
        }

        .status-code {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-right: 10px;
        }

        .status-200 {
            background: #d4edda;
            color: #155724;
        }

        .status-400 {
            background: #fff3cd;
            color: #856404;
        }

        .status-401 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-404 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-422 {
            background: #fff3cd;
            color: #856404;
        }

        .status-500 {
            background: #f8d7da;
            color: #721c24;
        }

        .note {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }

        .note h5 {
            margin-top: 0;
            color: #28a745;
        }

        .auth-required {
            background: #fff3cd;
            color: #856404;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 0.9em;
            margin-bottom: 15px;
            display: inline-block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .nav ul {
                grid-template-columns: 1fr;
            }

            .param-table {
                font-size: 0.9em;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚙 Driver API Documentation</h1>
            <p>SWT App - Ride Sharing Platform for Drivers</p>
            <p><strong>Base URL:</strong> <code>https://your-domain.com/api/driver</code></p>
        </div>

        <div class="nav">
            <h3>📋 Quick Navigation</h3>
            <ul>
                <li><a href="#authentication">🔐 Authentication</a></li>
                <li><a href="#profile">👤 Profile Management</a></li>
                <li><a href="#verification">✅ Verification</a></li>
                <li><a href="#rides">🚗 Ride Management</a></li>
                <li><a href="#earnings">💰 Earnings & Withdrawals</a></li>
                <li><a href="#reviews">⭐ Reviews</a></li>
                <li><a href="#support">🎧 Support</a></li>
            </ul>
        </div>

        <!-- Authentication Section -->
        <div class="section" id="authentication">
            <h2 class="section-header">🔐 Authentication</h2>
            <div class="section-content">

                <!-- Driver Login -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/login</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Driver login with email and password authentication.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Driver's email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Driver's password</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "login_success",
  "status": "success",
  "message": {
    "success": ["Login Successful"]
  },
  "data": {
    "driver": {
      "id": 1,
      "firstname": "John",
      "lastname": "Driver",
      "email": "<EMAIL>",
      "username": "johndriver",
      "mobile": "1234567890",
      "dial_code": "+1",
      "profile_complete": 1,
      "dv": 1,
      "vv": 1,
      "online_status": 0,
      "balance": "150.50",
      "avg_rating": "4.85"
    },
    "access_token": "1|drivertoken123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>

                            <p><span class="status-code status-401">401</span> Invalid Credentials</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "invalid_credential",
  "status": "error",
  "message": {
    "error": ["Invalid email or password"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Driver Registration -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/register</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="description">
                            Register a new driver account with mobile verification.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">firstname</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Driver's first name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">email</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Valid email address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">mobile</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Mobile number without country code</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">mobile_code</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Country dial code (e.g., +1, +44)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Password (min 6 characters)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">password_confirmation</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Password confirmation</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "registration_success",
  "status": "success",
  "message": {
    "success": ["Registration successful"]
  },
  "data": {
    "driver": {
      "id": 2,
      "firstname": "John",
      "email": "<EMAIL>",
      "mobile": "1234567890",
      "dial_code": "+1",
      "profile_complete": 0,
      "dv": 0,
      "vv": 0
    },
    "access_token": "2|newdrivertoken123456...",
    "token_type": "Bearer"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Management Section -->
        <div class="section" id="profile">
            <h2 class="section-header">👤 Profile Management</h2>
            <div class="section-content">

                <!-- Driver Details -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/driver-details</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get comprehensive driver information including personal details, vehicle info, statistics,
                            and recent reviews.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "driver_details_success",
  "status": "success",
  "message": {
    "success": ["Driver details retrieved successfully"]
  },
  "data": {
    "driver": {
      "id": 1,
      "personal_info": {
        "firstname": "John",
        "lastname": "Doe",
        "fullname": "John Doe",
        "username": "john_driver",
        "email": "<EMAIL>",
        "mobile": "1234567890",
        "dial_code": "+1",
        "mobile_number": "+11234567890",
        "image": "https://example.com/path/to/image.jpg",
        "address": "123 Main St",
        "city": "New York",
        "state": "NY",
        "zip": "10001",
        "country_name": "United States"
      },
      "professional_info": {
        "balance": "1,250.00",
        "online_status": 1,
        "status": 1,
        "profile_complete": 1,
        "total_reviews": 45,
        "avg_rating": "4.75"
      },
      "verification_status": {
        "email_verified": 1,
        "mobile_verified": 1,
        "document_verified": 1,
        "vehicle_verified": 1,
        "two_factor_enabled": 0
      },
      "service": {
        "id": 1,
        "name": "Car",
        "image": "car_icon.png"
      },
      "zone": {
        "id": 1,
        "name": "Downtown",
        "status": 1
      },
      "vehicle": {
        "id": 1,
        "vehicle_number": "ABC123",
        "model": {
          "id": 1,
          "name": "Camry"
        },
        "color": {
          "id": 1,
          "name": "White"
        },
        "year": {
          "id": 1,
          "name": "2022"
        },
        "brand": {
          "id": 1,
          "name": "Toyota"
        },
        "image": "vehicle_image.jpg"
      },
      "statistics": {
        "total_rides": 150,
        "completed_rides": 145,
        "total_earnings": "5,250.00",
        "average_rating": "4.75",
        "total_reviews": 45,
        "completion_rate": "96.67"
      },
      "recent_reviews": [
        {
          "id": 1,
          "rating": 5,
          "comment": "Excellent driver!",
          "created_at": "2024-01-15 10:30:00",
          "rider": {
            "name": "Jane Smith"
          }
        }
      ]
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Driver Data Submit -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/driver-data-submit</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Submit driver profile completion data including address and zone information.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">address</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Driver's address</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">zip</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Postal/ZIP code</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">country</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Country name</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">country_code</td>
                                        <td class="param-type">string</td>
                                        <td class="required">Required</td>
                                        <td>Country code (e.g., US, UK)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">zone</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Service zone ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "profile_updated",
  "status": "success",
  "message": {
    "success": ["Profile updated successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Ride Management Section -->
        <div class="section" id="rides">
            <h2 class="section-header">🚗 Ride Management</h2>
            <div class="section-content">

                <!-- Get Rides List -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/rides/list</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get list of rides assigned to the driver with filtering options.
                        </div>

                        <div class="params-section">
                            <h4>📥 Query Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">status</td>
                                        <td class="param-type">string</td>
                                        <td class="optional">Optional</td>
                                        <td>Filter by status (pending, running, completed, cancelled)</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">page</td>
                                        <td class="param-type">integer</td>
                                        <td class="optional">Optional</td>
                                        <td>Page number for pagination</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">limit</td>
                                        <td class="param-type">integer</td>
                                        <td class="optional">Optional</td>
                                        <td>Number of rides per page (default: 10)</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "rides_list",
  "status": "success",
  "message": {
    "success": ["Rides retrieved successfully"]
  },
  "data": {
    "rides": [
      {
        "id": 123,
        "uid": "RIDE123456",
        "pickup_location": "123 Main St",
        "destination": "456 Oak Ave",
        "ride_option": "go_now",
        "estimated_duration": 30,
        "cycles_count": 1,
        "fare_per_cycle": "25.00",
        "amount": "25.00",
        "case_code": "BCG2024001",
        "status": 1,
        "started_at": null,
        "ended_at": null,
        "user": {
          "id": 1,
          "name": "John User",
          "mobile": "9876543210",
          "employ_type": "BCG_EMPLOYEE"
        },
        "created_at": "2024-01-01T10:00:00.000000Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_rides": 50,
      "per_page": 10
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Start Ride -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/rides/start/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Start a ride when driver arrives at pickup location.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_started",
  "status": "success",
  "message": {
    "success": ["Ride started successfully"]
  },
  "data": {
    "ride": {
      "id": 123,
      "status": 2,
      "started_at": "2024-01-01T10:30:00.000000Z",
      "current_cycle": 1,
      "elapsed_time_minutes": 0
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- End Ride -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/rides/end/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            End a ride and calculate final billing based on actual duration.
                        </div>

                        <div class="params-section">
                            <h4>📥 URL Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Ride ID</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "ride_completed",
  "status": "success",
  "message": {
    "success": ["Ride completed successfully"]
  },
  "data": {
    "ride": {
      "id": 123,
      "status": 3,
      "started_at": "2024-01-01T10:30:00.000000Z",
      "ended_at": "2024-01-01T11:15:00.000000Z",
      "total_duration_minutes": 45,
      "cycles_used": 2,
      "final_amount": "50.00",
      "driver_earnings": "42.50",
      "commission": "7.50"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Update Live Location -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/rides/live-location/{id}</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Update driver's live location during an active ride.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">latitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Current latitude</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">longitude</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Current longitude</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "location_updated",
  "status": "success",
  "message": {
    "success": ["Location updated successfully"]
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Earnings & Withdrawals Section -->
        <div class="section" id="earnings">
            <h2 class="section-header">💰 Earnings & Withdrawals</h2>
            <div class="section-content">

                <!-- Get Earnings History -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="endpoint-url">/payment/history</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Get driver's earnings history and payment details.
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "payment_history",
  "status": "success",
  "message": {
    "success": ["Payment history retrieved"]
  },
  "data": {
    "payments": [
      {
        "id": 1,
        "ride_id": 123,
        "amount": "42.50",
        "commission": "7.50",
        "net_earning": "35.00",
        "payment_date": "2024-01-01T11:15:00.000000Z",
        "ride": {
          "uid": "RIDE123456",
          "pickup_location": "123 Main St",
          "destination": "456 Oak Ave"
        }
      }
    ],
    "summary": {
      "total_earnings": "1,250.00",
      "total_rides": 50,
      "average_per_ride": "25.00",
      "current_balance": "150.50"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Withdraw Request -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/withdraw-request</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Request withdrawal of earnings to driver's bank account.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">method_id</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>Withdrawal method ID</td>
                                    </tr>
                                    <tr>
                                        <td class="param-name">amount</td>
                                        <td class="param-type">numeric</td>
                                        <td class="required">Required</td>
                                        <td>Amount to withdraw</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "withdraw_request_success",
  "status": "success",
  "message": {
    "success": ["Withdrawal request submitted successfully"]
  },
  "data": {
    "withdrawal": {
      "id": 1,
      "amount": "100.00",
      "charge": "2.00",
      "final_amount": "98.00",
      "status": "pending",
      "created_at": "2024-01-01T12:00:00.000000Z"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Online Status Section -->
        <div class="section" id="status">
            <h2 class="section-header">🟢 Online Status</h2>
            <div class="section-content">

                <!-- Update Online Status -->
                <div class="endpoint">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="endpoint-url">/online-status</span>
                    </div>
                    <div class="endpoint-content">
                        <div class="auth-required">🔒 Authentication Required</div>
                        <div class="description">
                            Update driver's online/offline status for receiving ride requests.
                        </div>

                        <div class="params-section">
                            <h4>📥 Request Parameters</h4>
                            <table class="param-table">
                                <thead>
                                    <tr>
                                        <th>Parameter</th>
                                        <th>Type</th>
                                        <th>Required</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="param-name">status</td>
                                        <td class="param-type">integer</td>
                                        <td class="required">Required</td>
                                        <td>1 for online, 0 for offline</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="response-section">
                            <h4>📤 Response</h4>

                            <p><span class="status-code status-200">200</span> Success</p>
                            <div class="code-block">
                                <pre class="json">{
  "remark": "status_updated",
  "status": "success",
  "message": {
    "success": ["Online status updated successfully"]
  },
  "data": {
    "online_status": 1,
    "status_text": "Online"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="note">
            <h5>📝 Important Notes</h5>
            <ul>
                <li><strong>Authentication:</strong> All protected endpoints require Bearer token in Authorization
                    header</li>
                <li><strong>Verification Required:</strong> Driver and vehicle verification must be completed before
                    accepting rides</li>
                <li><strong>BCG Corporate Rides:</strong> Automatic assignment system - no bidding required</li>
                <li><strong>Billing Cycles:</strong> Rides are billed in 30-minute cycles with automatic calculation
                </li>
                <li><strong>Real-time Updates:</strong> Location updates are required during active rides</li>
                <li><strong>Commission Structure:</strong> Platform commission is automatically deducted from earnings
                </li>
            </ul>
        </div>

        <div class="section">
            <h2 class="section-header">📞 Support & Contact</h2>
            <div class="section-content">
                <p>For technical support or API questions, please contact:</p>
                <ul>
                    <li><strong>Email:</strong> <EMAIL></li>
                    <li><strong>Driver Portal:</strong> <a href="#" target="_blank">Driver Dashboard</a></li>
                    <li><strong>Emergency Support:</strong> +1-800-DRIVER-HELP</li>
                </ul>
            </div>
        </div>
    </div>
</body>

</html>